import React from 'react';
import { Breadcrumb as AntBreadcrumb } from 'antd';
import { HomeOutlined, UserOutlined } from '@ant-design/icons';

interface BreadcrumbProps {
  url: string;
  text: string;
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({ url, text }) => {
  const items = [
    {
      href: '/',
      title: <HomeOutlined />,
    },
    {
      href: url,
      title: (
        <>
          <UserOutlined />
          <span>{text}</span>
        </>
      ),
    },
  ];

  return <AntBreadcrumb items={items} style={{ margin: '6px 0 6px 8px', width: 'auto' }} />;
};

export default Breadcrumb;
