import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import ReactMde from 'react-mde';
import { Input } from 'antd';
import 'react-mde/lib/styles/css/react-mde-all.css';
import '@/styles/MarkdownEditor.css';

const { TextArea } = Input;

interface MarkdownEditorProps {
  value: string;
  needToolBar?: boolean;
  onChange?: (value: string) => void;
  readOnly?: boolean;
  disableMarkdown?: boolean; // 新增：禁用 markdown 支持
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({
  value,
  needToolBar = true,
  onChange,
  readOnly,
  disableMarkdown = false,
}) => {
  const [selectedTab, setSelectedTab] = useState<'write' | 'preview'>('write');

  useEffect(() => {
    if (readOnly) {
      setSelectedTab('preview');
    }
  }, [readOnly]);

  // 如果禁用 markdown，使用普通的 TextArea
  if (disableMarkdown) {
    return (
      <div className="plain-text-editor">
        <TextArea
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          readOnly={readOnly}
          placeholder="Enter your rules here (plain text only)..."
          autoSize={readOnly ? false : { minRows: 8, maxRows: 20 }}
          rows={readOnly ? 12 : undefined}
          style={{
            fontSize: '14px',
            lineHeight: '1.6',
            fontFamily: 'monospace',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '12px',
            resize: readOnly ? 'none' : 'vertical',
            overflow: readOnly ? 'auto' : 'hidden',
            maxHeight: readOnly ? '300px' : 'none',
            backgroundColor: readOnly ? '#f9f9f9' : 'white',
          }}
        />
        {!readOnly && (
          <div
            style={{
              marginTop: '8px',
              fontSize: '12px',
              color: '#666',
              fontStyle: 'italic',
            }}
          >
            Note: Markdown formatting is disabled. Text will be saved as plain text.
          </div>
        )}
        {readOnly && (
          <div
            style={{
              marginTop: '8px',
              fontSize: '12px',
              color: '#888',
              fontStyle: 'italic',
            }}
          >
            Scroll to view full content
          </div>
        )}
      </div>
    );
  }

  // 原有的 markdown 编辑器
  return (
    <ReactMde
      value={value}
      onChange={onChange}
      selectedTab={selectedTab}
      onTabChange={setSelectedTab}
      readOnly={readOnly}
      generateMarkdownPreview={(markdown) =>
        Promise.resolve(
          <div className="markdown-content">
            <ReactMarkdown>{markdown}</ReactMarkdown>
          </div>
        )
      }
      childProps={{
        writeButton: {
          style: { width: '100%' },
        },
      }}
      classes={{
        toolbar: needToolBar ? '' : 'hidden',
      }}
    />
  );
};

export default MarkdownEditor;
