import { StrictMode } from 'react';
import { HashRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import enUS from 'antd/es/locale/en_US';
import 'dayjs/locale/zh-tw';
import { createRoot } from 'react-dom/client';
import { theme } from './styles/theme.ts';
import RootApp from './RootApp.tsx';
import './styles/main.css';
import './styles/custom-input.css';
import './utils/dayjs.ts';

const container = document.getElementById('root') as HTMLDivElement;

createRoot(container).render(
  <StrictMode>
    <HashRouter>
      <ConfigProvider locale={enUS} theme={theme}>
        <RootApp />
      </ConfigProvider>
    </HashRouter>
  </StrictMode>
);
