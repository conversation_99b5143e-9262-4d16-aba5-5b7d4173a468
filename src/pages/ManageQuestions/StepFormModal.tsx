import React, { useState, useEffect } from 'react';
import { Modal, Steps, Button, Form, message, Progress, Tooltip } from 'antd';
import { LeftOutlined, RightOutlined, CheckOutlined } from '@ant-design/icons';
import { getCookie } from '@/utils';
import axios from 'axios';

// 导入各个步骤组件
import BasicInfoStep from './steps/BasicInfoStep';
import ContentStep from './steps/ContentStep';
import MediaStep from './steps/MediaStep';
import TradingStep from './steps/TradingStep';

interface StepFormModalProps {
  eventId: number;
  visible: boolean;
  currentEvent: any;
  setData: (data: any) => void;
  setVisibel: (visible: boolean) => void;
  onSubmit: (values: any) => Promise<void>;
  onCancel: () => void;
}

const StepFormModal: React.FC<StepFormModalProps> = ({
  eventId,
  visible,
  currentEvent,
  setData,
  setVisibel,
  onSubmit,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [stepData, setStepData] = useState<any>({});

  const userRole = getCookie('jwt-role');

  // 定义步骤
  const steps = [
    {
      title: 'Basic Info',
      description: 'Slug & Language',
    },
    {
      title: 'Content',
      description: 'Title & Description',
    },
    {
      title: 'Media',
      description: 'Images',
    },
    {
      title: 'Trading',
      description: 'Order Settings',
    },
  ];

  // 重置表单和状态
  const resetForm = () => {
    form.resetFields();
    setCurrentStep(0);
    setStepData({});
  };

  // 处理取消
  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  // 获取当前步骤需要验证的字段
  const getCurrentStepFields = (): string[] => {
    switch (currentStep) {
      case 0: // Basic Info
        return ['slug'];
      case 1: // Content
        const languages = stepData.languages || ['en'];
        const contentFields: string[] = [];
        languages.forEach((lang: string) => {
          contentFields.push(`title_${lang}`);
        });
        return contentFields;
      case 2: // Media
        return ['icon', 'image'];
      case 3: // Trading
        return ['order_min_size', 'order_price_min_tick_size'];
      default:
        return [];
    }
  };

  // 下一步
  const nextStep = async () => {
    try {
      // 只验证当前步骤的必填字段
      const fieldsToValidate = getCurrentStepFields();
      if (fieldsToValidate.length > 0) {
        await form.validateFields(fieldsToValidate);
      }

      // 获取所有表单值（包括未验证的字段）
      const allValues = form.getFieldsValue();
      const newStepData = { ...stepData, ...allValues };

      setStepData(newStepData);
      setCurrentStep(currentStep + 1);
    } catch (error) {
      console.error('Validation failed:', error);
      message.error('Please fill in all required fields correctly');
    }
  };

  // 上一步
  const prevStep = () => {
    // 获取当前表单值并保存
    const allValues = form.getFieldsValue();
    const newStepData = { ...stepData, ...allValues };

    setStepData(newStepData);
    setCurrentStep(currentStep - 1);
  };

  // 最终提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const finalData = { ...stepData, ...values };

      setLoading(true);

      // 导入必要的工具函数
      const {
        formatRangePickerDates,
        decodeFormData,
        clearFormData,
        getLocalStorage,
        calculateImageUrl,
      } = await import('@/utils');
      const dayjs = await import('dayjs');

      // 处理日期范围（使用用户选择的日期，如果没有则使用event的日期作为后备）
      const userDateRange = finalData.date_range;
      const dateRange =
        userDateRange && userDateRange.length === 2
          ? userDateRange
          : [
              currentEvent?.start_date ? dayjs.default.utc(currentEvent.start_date) : null,
              currentEvent?.end_date ? dayjs.default.utc(currentEvent.end_date) : null,
            ];

      // 确保日期范围有效，否则抛出错误
      if (!dateRange[0] || !dateRange[1]) {
        throw new Error('Invalid date range: both start and end dates are required');
      }

      const { start_date, end_date } = formatRangePickerDates([dateRange[0], dateRange[1]]);
      const { slug, title, description } = decodeFormData(finalData);
      const { order_min_size, order_price_min_tick_size } = finalData;

      const submitted_by = getLocalStorage('userWallet');
      const timeStamps = dayjs.default.utc().format('YYYY-MM-DDTHH:mm:ss.SSS+00:00').toString();
      const clearValues = clearFormData(finalData);

      const order_min_size_num = Number(order_min_size) * 10 ** 6;
      const order_price_min_tick_size_num = Number(order_price_min_tick_size) + 2; // 2 is no decimal

      const formattedValues = {
        ...clearValues,
        start_date,
        end_date,
        slug,
        question: title,
        description,
        submitted_by,
        condition_id: 'PLACEHOLDER:' + timeStamps,
        created_at: timeStamps,
        updated_at: timeStamps,

        order_min_size: order_min_size_num,
        order_price_min_tick_size: order_price_min_tick_size_num,
        active: true,
        closed: false,

        icon: calculateImageUrl(clearValues, 'icon'),
        image: calculateImageUrl(clearValues, 'image'),
      };

      await onSubmit(formattedValues);

      // 建立question和event的关联
      const { fetchLinkEventQuestion, fetchQuestionList } = await import('@/api/questions');
      await fetchLinkEventQuestion({
        event_id: currentEvent.id,
        condition_id: 'PLACEHOLDER:' + timeStamps,
      });

      // 刷新数据列表
      const { decodeQuestionData } = await import('@/utils');
      const res = await fetchQuestionList(Number(eventId), submitted_by);
      setData(decodeQuestionData(res.data.events[0].event_markets));

      message.success('Question added successfully');
      setLoading(false);
      setVisibel(false);
      resetForm();
    } catch (error) {
      console.error('Submit failed:', error);

      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.error || error.response?.data?.message || error.message;
        message.error(`Add failed: ${errorMessage}`);
      } else {
        message.error('Add failed: An unexpected error occurred');
      }

      setLoading(false);
    }
  };

  // 渲染当前步骤的内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return <BasicInfoStep form={form} stepData={stepData} currentEvent={currentEvent} />;
      case 1:
        return <ContentStep form={form} stepData={stepData} currentEvent={currentEvent} />;
      case 2:
        return <MediaStep form={form} stepData={stepData} />;
      case 3:
        return <TradingStep form={form} stepData={stepData} currentEvent={currentEvent} />;
      default:
        return null;
    }
  };

  // 键盘事件处理
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (!visible) return;

      if (event.key === 'Enter' && event.ctrlKey) {
        // Ctrl+Enter: 下一步或提交
        if (currentStep < steps.length - 1) {
          nextStep();
        } else {
          handleSubmit();
        }
      } else if (event.key === 'Escape') {
        // Escape: 取消
        handleCancel();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [visible, currentStep, steps.length]);

  // 渲染底部按钮
  const renderFooter = () => {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          padding: '16px 24px',
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
          borderTop: '1px solid #e2e8f0',
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          {currentStep > 0 && (
            <Button
              onClick={prevStep}
              icon={<LeftOutlined />}
              style={{
                borderRadius: '8px',
                fontWeight: '500',
                height: '38px',
                padding: '0 20px',
                border: '1.5px solid #e2e8f0',
                background: 'white',
                color: '#64748b',
                fontSize: '16px',
                display: 'flex',
                alignItems: 'center',
                gap: '6px',
                transition: 'all 0.2s ease',
              }}
            >
              Previous
            </Button>
          )}
        </div>

        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Button
            onClick={handleCancel}
            style={{
              borderRadius: '8px',
              fontWeight: '500',
              height: '38px',
              padding: '0 20px',
              border: '1.5px solid #e2e8f0',
              background: 'white',
              color: '#64748b',
              fontSize: '16px',
              transition: 'all 0.2s ease',
            }}
          >
            Cancel
          </Button>

          {currentStep < steps.length - 1 ? (
            <Tooltip title="Press Ctrl+Enter to continue" placement="top">
              <Button
                type="primary"
                onClick={nextStep}
                icon={<RightOutlined />}
                iconPosition="end"
                style={{
                  borderRadius: '8px',
                  fontWeight: '600',
                  height: '38px',
                  padding: '0 20px',
                  background: 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)',
                  border: 'none',
                  fontSize: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  transition: 'all 0.2s ease',
                }}
              >
                Next Step
              </Button>
            </Tooltip>
          ) : (
            <Tooltip title="Press Ctrl+Enter to create question" placement="top">
              <Button
                type="primary"
                onClick={handleSubmit}
                loading={loading}
                icon={<CheckOutlined />}
                iconPosition="end"
                style={{
                  borderRadius: '8px',
                  fontWeight: '600',
                  height: '38px',
                  padding: '0 24px',
                  background: 'linear-gradient(135deg, #059669 0%, #10b981 100%)',
                  border: 'none',
                  boxShadow: '0 4px 12px rgba(5, 150, 105, 0.3)',
                  fontSize: '16px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '6px',
                  transition: 'all 0.2s ease',
                }}
              >
                {loading ? 'Creating...' : '🚀 Create Question'}
              </Button>
            </Tooltip>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <Modal
        width={1000}
        title={
          <div
            style={{
              textAlign: 'center',
              position: 'relative',
            }}
          >
            <div
              className="modal-title-gradient"
              style={{
                fontSize: '28px',
                fontWeight: '700',
                letterSpacing: '0.8px',
                position: 'relative',
                display: 'inline-block',
                fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif',
              }}
            >
              ✨ Create Question - Step by Step ✨
            </div>
            <div
              style={{
                position: 'absolute',
                bottom: '2px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '140px',
                height: '3px',
                background:
                  'linear-gradient(90deg, transparent 0%, #667eea 20%, #764ba2 40%, #f093fb 60%, #667eea 80%, transparent 100%)',
                borderRadius: '3px',
                opacity: 0.7,
                boxShadow: '0 1px 3px rgba(102, 126, 234, 0.3)',
              }}
            />
          </div>
        }
        open={visible}
        onCancel={handleCancel}
        footer={renderFooter()}
        maskClosable={false}
        className="step-form-modal"
        styles={{
          header: {
            padding: '16px 24px 16px 24px',
            borderBottom: '2px solid #e2e8f0',
          },
        }}
      >
        <div style={{ display: 'flex' }}>
          {/* 左侧：步骤导航 */}
          <div
            style={{
              width: '320px',
              background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
              borderRight: '2px solid #e2e8f0',
              padding: '24px 20px',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* 进度指示 */}
            <div
              style={{
                marginBottom: '28px',
                padding: '20px',
                background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                borderRadius: '16px',
                border: '1px solid #e2e8f0',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '16px',
                }}
              >
                <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                  <div
                    style={{
                      width: '8px',
                      height: '8px',
                      borderRadius: '50%',
                      background: 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)',
                      boxShadow: '0 0 8px rgba(14, 165, 233, 0.4)',
                    }}
                  ></div>
                  <span
                    style={{
                      fontSize: '16px',
                      color: '#374151',
                      fontWeight: '600',
                      letterSpacing: '0.3px',
                    }}
                  >
                    Step {currentStep + 1} of {steps.length}
                  </span>
                </div>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                  }}
                >
                  <span
                    style={{
                      fontSize: '15px',
                      color: '#0ea5e9',
                      fontWeight: '700',
                      background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                      padding: '4px 10px',
                      borderRadius: '12px',
                      border: '1px solid #bae6fd',
                      boxShadow: '0 2px 4px rgba(14, 165, 233, 0.1)',
                    }}
                  >
                    {Math.round(((currentStep + 1) / steps.length) * 100)}%
                  </span>
                </div>
              </div>
              <div style={{ width: '100%' }}>
                <Progress
                  className="step-progress-bar"
                  percent={((currentStep + 1) / steps.length) * 100}
                  showInfo={false}
                  strokeColor={{
                    '0%': '#0ea5e9',
                    '30%': '#3b82f6',
                    '70%': '#6366f1',
                    '100%': '#8b5cf6',
                  }}
                  trailColor="#e2e8f0"
                  size={14}
                  style={{
                    marginBottom: '8px',
                    width: '100%',
                  }}
                  strokeLinecap="round"
                />
              </div>
            </div>

            {/* Steps组件 */}
            <Steps
              current={currentStep}
              direction="vertical"
              size="small"
              items={steps.map((step, index) => ({
                ...step,
                title: (
                  <div
                    style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color:
                        currentStep > index
                          ? '#059669'
                          : currentStep === index
                            ? '#0ea5e9'
                            : '#94a3b8',
                      lineHeight: '1.4',
                    }}
                  >
                    {step.title}
                  </div>
                ),
                icon: (
                  <div
                    style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontSize: '13px',
                      fontWeight: 'bold',
                      background:
                        currentStep > index
                          ? 'linear-gradient(135deg, #059669 0%, #10b981 100%)'
                          : currentStep === index
                            ? 'linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%)'
                            : '#f1f5f9',
                      color: currentStep >= index ? 'white' : '#94a3b8',
                      border: currentStep === index ? '3px solid #bfdbfe' : '2px solid transparent',
                      boxShadow:
                        currentStep > index
                          ? '0 4px 12px rgba(5, 150, 105, 0.3)'
                          : currentStep === index
                            ? '0 4px 12px rgba(14, 165, 233, 0.3)'
                            : 'none',
                      transition: 'all 0.3s ease',
                    }}
                  >
                    {currentStep > index ? '✓' : index + 1}
                  </div>
                ),
              }))}
              style={{
                background: 'white',
                padding: '24px 20px',
                borderRadius: '16px',
                boxShadow: '0 4px 16px rgba(0,0,0,0.06)',
                border: '1px solid #e2e8f0',
              }}
            />
          </div>

          {/* 右侧：表单内容 */}
          <div
            style={{
              flex: 1,
              padding: '8px',
              position: 'relative',
              background: 'linear-gradient(135deg, #ffffff 0%, #fefefe 100%)',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            {/* 表单内容区域 - 使用flex布局合理分布 */}
            <div
              style={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'flex-start',
                overflow: 'auto',
                paddingRight: '8px',
              }}
            >
              <Form
                form={form}
                layout="vertical"
                preserve={false}
                validateTrigger={['onChange', 'onBlur']}
                style={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <div
                  style={{
                    flex: 1,
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'flex-start',
                  }}
                >
                  {renderStepContent()}
                </div>
              </Form>
            </div>
          </div>
        </div>
      </Modal>

      <style>
        {`
          @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
          }

          .modal-title-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
          }

          .step-modal-content .ant-steps-item-title {
            font-size: 17px !important;
            font-weight: 600 !important;
            line-height: 1.4 !important;
            margin-bottom: 4px !important;
          }

          .step-modal-content .ant-steps-item-description {
            font-size: 14px !important;
            color: #64748b !important;
            margin-top: 0 !important;
            font-weight: 400 !important;
          }

          .step-modal-content .ant-steps-item-icon {
            margin-right: 16px !important;
            margin-top: 2px !important;
          }

          .step-modal-content .ant-steps-item:not(:last-child) .ant-steps-item-container .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #e2e8f0 0%, #f1f5f9 100%) !important;
            width: 2px !important;
            left: 15px !important;
          }

          .step-modal-content .ant-steps-item-finish .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #059669 0%, #10b981 100%) !important;
            width: 3px !important;
            left: 14.5px !important;
          }

          .step-modal-content .ant-steps-item-process .ant-steps-item-tail::after {
            background: linear-gradient(to bottom, #0ea5e9 0%, #3b82f6 100%) !important;
            width: 3px !important;
            left: 14.5px !important;
          }

          .step-progress-bar {
            width: 100% !important;
          }

          .step-progress-bar .ant-progress-outer {
            width: 100% !important;
          }

          .step-progress-bar .ant-progress-inner {
            width: 100% !important;
            border-radius: 5px !important;
            background: #e2e8f0 !important;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1) !important;
          }

          .step-progress-bar .ant-progress-bg {
            border-radius: 5px !important;
            background: linear-gradient(90deg, #0ea5e9 0%, #3b82f6 50%, #8b5cf6 100%) !important;
          }

          .ant-modal-content {
            border-radius: 12px !important;
            overflow: hidden !important;
          }

          .ant-modal-header {
            border-radius: 12px 12px 0 0 !important;
          }

          .step-form-modal .ant-form-item-label > label {
            font-weight: 600 !important;
            color: #374151 !important;
            font-size: 14px !important;
          }

          /* 只对StepFormModal内部没有特定className的Input组件应用全局样式，排除custom-input */
          .step-form-modal .ant-form-item:not(.custom-input) .ant-input:not(.content-step-input):not(.basic-info-input):not(.trading-step-input),
          .step-form-modal .ant-form-item:not(.custom-input) .ant-select-selector:not(.content-step-select):not(.basic-info-select),
          .step-form-modal .ant-form-item:not(.custom-input) .ant-picker:not(.trading-step-picker),
          .step-form-modal .ant-form-item:not(.custom-input) .ant-input-number:not(.trading-step-input) {
            border-radius: 8px !important;
            border: 1.5px solid #e2e8f0 !important;
            transition: all 0.2s ease !important;
          }

          .step-form-modal .ant-form-item:not(.custom-input) .ant-input:not(.content-step-input):not(.basic-info-input):not(.trading-step-input):focus,
          .step-form-modal .ant-form-item:not(.custom-input) .ant-select-focused .ant-select-selector:not(.content-step-select):not(.basic-info-select),
          .step-form-modal .ant-form-item:not(.custom-input) .ant-picker-focused:not(.trading-step-picker),
          .step-form-modal .ant-form-item:not(.custom-input) .ant-input-number-focused:not(.trading-step-input) {
            border-color: #0ea5e9 !important;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1) !important;
          }

          .step-form-modal .ant-btn-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #3b82f6 100%) !important;
            border: none !important;
            border-radius: 8px !important;
            font-weight: 600 !important;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3) !important;
            transition: all 0.2s ease !important;
          }

          .step-form-modal .ant-btn-primary:hover {
            background: linear-gradient(135deg, #0284c7 0%, #2563eb 100%) !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4) !important;
          }

          .step-form-modal .ant-btn-default {
            border-radius: 8px !important;
            border: 1.5px solid #e2e8f0 !important;
            font-weight: 500 !important;
            transition: all 0.2s ease !important;
          }

          .step-form-modal .ant-btn-default:hover {
            border-color: #0ea5e9 !important;
            color: #0ea5e9 !important;
            transform: translateY(-1px) !important;
          }
        `}
      </style>
    </>
  );
};

export default StepFormModal;
