import React, { useState } from 'react';
import { Modal, Form, Input, Radio, message, Row, Col, Divider } from 'antd';
import handlePublishResult from '@/utils/signature/createPublishResult';
import { CheckCircleOutlined, SnippetsOutlined } from '@ant-design/icons';

interface ResultModalProps {
  record: any;
  loading: boolean;
  setLoading: (loading: boolean) => void;
  visible: boolean;
  fetchData: () => void;
  onCancel: () => void;
}

const ResultModal: React.FC<ResultModalProps> = ({
  record,
  loading,
  setLoading,
  visible,
  fetchData,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [selectedOutcome, setSelectedOutcome] = useState<string>('');
  const [confirmation, setConfirmation] = useState<string>('');
  const [resultHash, setResultHash] = useState<string>('');
  const targetUrl = `${import.meta.env.VITE_PUBLIC_BLOCK_EXPLOER}/${resultHash}`;

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (values.confirmation.toUpperCase() !== selectedOutcome) {
        message.error('Results do not match');
        return;
      }

      setLoading(true);
      const outcome = selectedOutcome === 'YES' ? 'TRUE' : 'FALSE';
      const res = await handlePublishResult(record, outcome);
      console.log('res', res);
      fetchData();
      setResultHash(res?.result_tx);
    } catch (error) {
      message.error(`Error: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      width={600}
      title="Publish Result"
      open={visible}
      onOk={handleOk}
      onCancel={() => {
        onCancel();
        setResultHash('');
      }}
      confirmLoading={loading}
      footer={resultHash ? null : undefined}
      okText="Confirm"
      cancelText="Cancel"
    >
      {resultHash ? (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            padding: '24px',
          }}
        >
          <CheckCircleOutlined style={{ fontSize: '48px', color: '#52c41a' }} />
          <div style={{ fontSize: '18px', color: '#52c41a', marginTop: '8px' }}>
            Event Published Successfully
          </div>
          <Divider dashed style={{ margin: '12px 0' }} />
          <a
            href={targetUrl}
            target="_blank"
            rel="noopener noreferrer"
            style={{ fontSize: '16px', color: '#2196F3', marginTop: '8px' }}
          >
            View on explorer <SnippetsOutlined style={{ marginLeft: '4px' }} />
          </a>
        </div>
      ) : (
        <Form form={form} layout="vertical">
          <Row>
            <Col span={24}>
              <Form.Item
                name="outcome"
                label="Result"
                rules={[{ required: true, message: 'Please select a result' }]}
              >
                <Radio.Group onChange={(e) => setSelectedOutcome(e.target.value)}>
                  <Radio value="YES">YES</Radio>
                  <Radio value="NO">NO</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item
                name="confirmation"
                label="Confirmation"
                rules={[{ required: true, message: `Please Input ${selectedOutcome}` }]}
              >
                <Input
                  value={confirmation}
                  onChange={(e) => setConfirmation(e.target.value)}
                  placeholder={`Please Input ${selectedOutcome}`}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
    </Modal>
  );
};

export default ResultModal;
