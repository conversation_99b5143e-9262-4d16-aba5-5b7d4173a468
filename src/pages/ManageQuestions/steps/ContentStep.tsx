import React, { useEffect, useState } from 'react';
import { Form, Input, FormInstance, Select } from 'antd';
import { decode, LANGUAGE_OPTIONS } from '@/utils';

interface ContentStepProps {
  form: FormInstance;
  stepData: any;
  currentEvent: any;
}

const ContentStep: React.FC<ContentStepProps> = ({ form, stepData, currentEvent }) => {
  const [languages, setLanguages] = useState<string[]>(['en']);

  // 处理语言数据的函数（与AddModal保持一致）
  const processLanguages = (languages: any) => {
    const arrLanguages = languages.filter((lang: any) => lang.language);
    return [...arrLanguages.map((lang: any) => lang.language)];
  };

  // 从currentEvent获取语言列表（与AddModal逻辑一致）
  useEffect(() => {
    if (currentEvent?.title) {
      const decodeTitle = decode(currentEvent.title);
      if (decodeTitle) {
        const matchedLanguages = processLanguages(decodeTitle);
        setLanguages(matchedLanguages);
      }
    }
  }, [currentEvent?.title]);

  // 将languages添加到stepData中，供后续步骤使用
  useEffect(() => {
    if (!stepData.languages) {
      // 这里不直接修改stepData，而是通过表单值来传递
      form.setFieldValue('languages', languages);
    }
  }, [form, stepData.languages, languages]);

  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};
    languages.forEach((language: string) => {
      if (stepData[`title_${language}`]) {
        initialValues[`title_${language}`] = stepData[`title_${language}`];
      }
    });

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [stepData, form, languages]);

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .content-step-select .ant-select-selector {
            border: none !important;
            outline: none !important;
            box-shadow: 0 0 0 2px #e5e7eb, 0 2px 8px rgba(0, 0, 0, 0.04) !important;
            border-radius: 12px !important;
            transition: all 0.2s ease !important;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
            padding: 8px 12px !important;
            min-height: 48px !important;
          }

        `}
      </style>

      <div style={{ maxWidth: '900px', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '30px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #06b6d4, #0891b2)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(139, 92, 246, 0.3)',
              }}
            >
              📝
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  letterSpacing: '0.4px',
                }}
              >
                Question Content
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                Please create a title for the region supported by your question
              </div>
            </div>
          </div>

          {/* 语言显示区域 */}
          <div
            style={{
              marginBottom: '12px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                fontSize: '22px',
                fontWeight: '700',
                color: '#1f2937',
                marginBottom: '8px',
                letterSpacing: '0.3px',
              }}
            >
              🌐 Supported Languages
            </div>
            <Select
              className="content-step-select"
              mode="multiple"
              value={languages}
              options={LANGUAGE_OPTIONS}
              disabled
              size="large"
              style={{
                width: '100%',
                fontSize: '16px',
              }}
              placeholder="Languages from parent event"
            />
            <p
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
                fontStyle: 'italic',
                margin: '12px 0 0 0',
              }}
            >
              Languages are inherited from the parent event and cannot be modified
            </p>
          </div>

          {/* 表单内容 */}
          <div
            style={{
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* Question Titles Section */}
            <div>
              <div
                style={{
                  fontSize: '22px',
                  fontWeight: '700',
                  color: '#1f2937',
                  marginBottom: '8px',
                  letterSpacing: '0.3px',
                }}
              >
                📝 Question Titles
              </div>

              {languages.map((language: string, index: number) => {
                const langName =
                  {
                    en: '🇺🇸 English',
                    zh: '🇨🇳 Chinese',
                    ja: '🇯🇵 Japanese',
                    ko: '🇰🇷 Korean',
                  }[language] || `🌐 ${language.toUpperCase()}`;

                return (
                  <div key={`${language}_${index}`} style={{ margin: '12px 0' }}>
                    <Form.Item
                      name={`title_${language}`}
                      label={
                        <span
                          style={{
                            fontSize: '18px',
                            fontWeight: '600',
                            color: '#374151',
                            letterSpacing: '0.2px',
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                          }}
                        >
                          {langName.split(' ')[0]}{' '}
                          {langName.split(' ')[1] || language.toUpperCase()}
                        </span>
                      }
                      validateTrigger={['onChange', 'onBlur']}
                      rules={[{ required: true, message: 'Title is required', whitespace: true }]}
                      style={{ marginBottom: '16px' }}
                      className="custom-input"
                    >
                      <Input
                        placeholder={`Enter question title in ${langName.split(' ')[1] || language}...`}
                        size="large"
                        onChange={(e) => {
                          // 确保表单值被正确设置
                          form.setFieldsValue({ [`title_${language}`]: e.target.value });
                        }}
                      />
                    </Form.Item>

                    {index < languages.length - 1 && (
                      <div
                        style={{
                          height: '1px',
                          background:
                            'linear-gradient(90deg, transparent 0%, #e2e8f0 20%, #e2e8f0 80%, transparent 100%)',
                          margin: '16px 0',
                        }}
                      />
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ContentStep;
