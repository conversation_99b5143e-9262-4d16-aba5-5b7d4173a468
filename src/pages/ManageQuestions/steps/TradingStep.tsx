import React, { useEffect } from 'react';
import { Form, InputNumber, FormInstance, Tooltip, DatePicker, Row, Col } from 'antd';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface TradingStepProps {
  form: FormInstance;
  stepData: any;
  currentEvent: any;
}

const TradingStep: React.FC<TradingStepProps> = ({ form, stepData, currentEvent }) => {
  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};

    if (stepData.order_min_size !== undefined) {
      initialValues.order_min_size = stepData.order_min_size;
    } else {
      initialValues.order_min_size = 1; // 默认值
    }

    if (stepData.order_price_min_tick_size !== undefined) {
      initialValues.order_price_min_tick_size = stepData.order_price_min_tick_size;
    } else {
      initialValues.order_price_min_tick_size = 0; // 默认值
    }

    // 设置日期范围（从当前事件获取，通常是禁用的）
    if (currentEvent?.start_date && currentEvent?.end_date) {
      initialValues.date_range = [
        dayjs.utc(currentEvent.start_date),
        dayjs.utc(currentEvent.end_date),
      ];
    }

    form.setFieldsValue(initialValues);
  }, [stepData, form, currentEvent]);

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          @keyframes pulse {
            0%, 100% {
              transform: scale(1);
            }
            50% {
              transform: scale(1.05);
            }
          }
          .trading-step-input .ant-input-number {
            border: none !important;
            outline: none !important;
            box-shadow: 0 0 0 2px #e5e7eb, 0 2px 8px rgba(0, 0, 0, 0.04) !important;
            border-radius: 12px !important;
            font-size: 16px !important;
            transition: all 0.2s ease !important;
            padding: 12px 16px !important;
          }
          .trading-step-input .ant-input-number:hover {
            box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.15) !important;
          }
          .trading-step-input .ant-input-number:focus,
          .trading-step-input .ant-input-number-focused {
            box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.25) !important;
          }
          .trading-step-picker .ant-picker {
            border: none !important;
            outline: none !important;
            box-shadow: 0 0 0 2px #e5e7eb, 0 2px 8px rgba(0, 0, 0, 0.04) !important;
            border-radius: 12px !important;
            font-size: 16px !important;
            transition: all 0.2s ease !important;
            padding: 12px 16px !important;
          }
          .trading-step-picker .ant-picker:hover {
            box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.15) !important;
          }
          .trading-step-picker .ant-picker:focus,
          .trading-step-picker .ant-picker-focused {
            box-shadow: 0 0 0 2px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.25) !important;
          }
        `}
      </style>

      {/* 整体表单容器 */}
      <div
        style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '2px solid #e2e8f0',
          borderRadius: '24px',
          padding: '40px',
          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
          position: 'relative',
          overflow: 'hidden',
          width: '100%',
          height: '100%',
        }}
      >
        {/* 装饰性背景元素 */}
        <div
          style={{
            position: 'absolute',
            top: '-40px',
            right: '-40px',
            width: '120px',
            height: '120px',
            background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
            borderRadius: '50%',
            opacity: '0.04',
          }}
        ></div>
        <div
          style={{
            position: 'absolute',
            bottom: '-50px',
            left: '-50px',
            width: '150px',
            height: '150px',
            background: 'linear-gradient(135deg, #06b6d4, #0891b2)',
            borderRadius: '50%',
            opacity: '0.03',
          }}
        ></div>

        {/* 表单标题 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            marginBottom: '20px',
            paddingBottom: '20px',
            borderBottom: '3px solid #e2e8f0',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              width: '56px',
              height: '56px',
              background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
              borderRadius: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              boxShadow: '0 6px 16px rgba(139, 92, 246, 0.3)',
            }}
          >
            💰
          </div>
          <div>
            <div
              style={{
                fontSize: '28px',
                fontWeight: '800',
                color: '#1f2937',
                letterSpacing: '0.4px',
              }}
            >
              Trading Configuration
            </div>
            <div
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
              }}
            >
              Set trading parameters and schedule for your question
            </div>
          </div>
        </div>

        {/* 表单内容 */}
        <div
          style={{
            position: 'relative',
            zIndex: 1,
            animation: 'slideInUp 0.6s ease-out',
          }}
        >
          <div>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  name="order_min_size"
                  label={
                    <span
                      style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#374151',
                        letterSpacing: '0.2px',
                      }}
                    >
                      <Tooltip title="Minimum dollar amount per order for this question. Default is $1.">
                        💵 Min Amount
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: 'Minimum order amount is required' }]}
                  style={{ marginBottom: '8px' }}
                >
                  <InputNumber<number>
                    className="trading-step-input"
                    style={{ width: '100%' }}
                    min={1}
                    size="large"
                    formatter={(value) => `$ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                    parser={(value) => value?.replace(/\$\s?|(,*)/g, '') as unknown as number}
                    placeholder="Enter minimum amount (e.g., $1)"
                  />
                </Form.Item>

                <div
                  style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    fontWeight: '500',
                    fontStyle: 'italic',
                    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                    padding: '12px 16px',
                    borderRadius: '8px',
                    border: '1px solid #fbbf24',
                    marginBottom: '16px',
                  }}
                >
                  💡 <strong>Min Amount:</strong> Minimum dollar amount per order (default $1)
                </div>
              </Col>

              <Col span={12}>
                <Form.Item
                  name="order_price_min_tick_size"
                  label={
                    <span
                      style={{
                        fontSize: '20px',
                        fontWeight: '600',
                        color: '#374151',
                        letterSpacing: '0.2px',
                      }}
                    >
                      <Tooltip title="Decimal places allowed for share quantities. 0 = whole shares only, 1 = one decimal place, etc.">
                        ⚙️ Min Tick Size
                      </Tooltip>
                    </span>
                  }
                  rules={[{ required: true, message: 'Minimum tick size is required' }]}
                  style={{ marginBottom: '16px' }}
                >
                  <InputNumber
                    className="trading-step-input"
                    style={{ width: '100%' }}
                    min={0}
                    max={6}
                    size="large"
                    placeholder="Enter decimal places (e.g., 0)"
                  />
                </Form.Item>

                <div
                  style={{
                    fontSize: '14px',
                    color: '#6b7280',
                    fontWeight: '500',
                    fontStyle: 'italic',
                    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',
                    padding: '12px 16px',
                    borderRadius: '8px',
                    border: '1px solid #fbbf24',
                    marginBottom: '16px',
                  }}
                >
                  💡 <strong>Min Tick Size:</strong> Decimal places for share quantities (0 = whole
                  shares, 1 = 0.1 shares, 2 = 0.01 shares, etc.)
                </div>
              </Col>
            </Row>
          </div>

          {/* 第二行：Schedule */}
          <div>
            <Form.Item
              name="date_range"
              label={
                <span
                  style={{
                    fontSize: '20px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  ⏰ Date Range
                </span>
              }
              rules={[{ required: true, message: 'Please select date range' }]}
              style={{ marginBottom: '16px' }}
            >
              <RangePicker
                size="large"
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                placeholder={['Start time', 'End time']}
                onChange={(dates) => {
                  form.setFieldsValue({ date_range: dates });
                }}
                style={{
                  width: '100%',
                  fontSize: '16px',
                  borderRadius: '12px',
                  border: '2px solid #e5e7eb',
                  padding: '12px 16px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                  transition: 'all 0.2s ease',
                }}
              />
            </Form.Item>

            <div
              style={{
                fontSize: '14px',
                color: '#6b7280',
                fontWeight: '500',
                fontStyle: 'italic',
                background: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%)',
                padding: '12px 16px',
                borderRadius: '8px',
                border: '1px solid #29b6f6',
              }}
            >
              🕒 Define the time period when users can place trades on this question
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TradingStep;
