import React, { useState, useEffect } from 'react';
import { Form, Input, FormInstance } from 'antd';

interface BasicInfoStepProps {
  form: FormInstance;
  stepData: any;
  currentEvent: any;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ form, stepData }) => {
  const [slugLength, setSlugLength] = useState(0);

  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};

    if (stepData.slug) {
      initialValues.slug = stepData.slug;
      setSlugLength(stepData.slug.length);
    }

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [stepData, form]);

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

        `}
      </style>

      <div style={{ width: '96%', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(59, 130, 246, 0.3)',
              }}
            >
              🔗
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  letterSpacing: '0.4px',
                }}
              >
                Question Slug
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                Set a unique identifier for your question
              </div>
            </div>
          </div>

          {/* 表单内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '32px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div>
              <Form.Item
                name="slug"
                label={
                  <span
                    style={{
                      fontSize: '24px',
                      fontWeight: '700',
                      color: '#1f2937',
                      letterSpacing: '0.3px',
                    }}
                  >
                    🔗 Question Slug
                  </span>
                }
                validateTrigger={['onChange', 'onBlur']}
                rules={[
                  {
                    required: true,
                    message: 'Please enter a slug for your question',
                    whitespace: true,
                  },
                  {
                    pattern: /^[a-zA-Z0-9_]+$/,
                    message: 'Only letters, numbers, and underscores are allowed',
                  },
                  {
                    min: 3,
                    message: 'Slug must be at least 3 characters long',
                  },
                ]}
                style={{ marginBottom: '16px' }}
                className="custom-input"
              >
                <Input
                  maxLength={30}
                  onChange={(e) => setSlugLength(e.target.value.length)}
                  suffix={
                    <span
                      style={{
                        color: slugLength > 30 ? '#ef4444' : '#6b7280',
                        fontWeight: '600',
                        fontSize: '14px',
                      }}
                    >
                      {slugLength}/30
                    </span>
                  }
                  placeholder="my_question_name"
                  size="large"
                />
              </Form.Item>

              <p
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                  fontStyle: 'italic',
                  margin: 0,
                }}
              >
                Unique identifier for your question URL (cannot be changed later)
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BasicInfoStep;
