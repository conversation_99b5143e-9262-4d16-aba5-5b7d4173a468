import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Image,
  Card,
  Row,
  Col,
  Typography,
  Tooltip,
  message,
  Dropdown,
  Tag,
} from 'antd';
import type { ColumnsType } from 'antd/es/table';
import type { MenuProps } from 'antd';
import axios from 'axios';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';

import { useLocation } from 'react-router-dom';
import {
  fetchQuestionList,
  fetchAddQuestion,
  fetchUpdateQuestion,
  fetchEventsById,
  fetchLinkEventQuestion,
} from '@/api/questions';
import {
  formatDate,
  decodeQuestionData,
  getLocalStorage,
  encodeContent,
  generateUniqueSlug,
  getCookie,
  validateJWTWithMessage,
} from '@/utils';
import { PoweroffOutlined, MoreOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import AddModal from './AddModal';
import EditModal from './DetailModal';
import ResultModal from './ResultModal';
import WarningModal from './WarningModal';
import handlePublishResult from '@/utils/signature/createPublishResult';

// 状态颜色映射
const statusColorMap = {
  yes: 'success' as const,
  no: 'error' as const,
};

const ManageQuestions: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isResultModalVisible, setIsResultModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [warningModalType, setWarningModalType] = useState<'delete' | 'publish'>('delete');
  const [isWarningModalVisible, setIsWarningModalVisible] = useState(false);
  const [loading, setLoading] = useState<{ [key: number]: boolean }>({});
  const [isResultLoading, setIsResultLoading] = useState(false);
  const [publishResultLoading, setPublishResultLoading] = useState<{ [key: number]: boolean }>({});
  const [currentEvent, setCurrentEvent] = useState<any>(null);
  const [questionId, setQuestionId] = useState<number | undefined>(undefined);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [copyData, setCopyData] = useState<any[]>([]);

  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const eventId = Number(params.get('eventId'));
  const submitted_by = getLocalStorage('userWallet');

  const fetchData = async () => {
    if (eventId) {
      try {
        const res = await fetchQuestionList(Number(eventId), submitted_by);
        if (res.data.events?.[0]?.event_markets) {
          setData(decodeQuestionData(res.data.events?.[0]?.event_markets));
          setCopyData(decodeQuestionData(res.data.events?.[0]?.event_markets));
        } else {
          setData([]);
        }
        const eventRes = await fetchEventsById(Number(eventId));
        const event = eventRes.data.events?.[0];
        setCurrentEvent(event);
      } catch (error) {
        console.error('Error fetching question by event ID:', error);
      }
    }
  };

  useEffect(() => {
    fetchData();
  }, [location.search]);

  // 处理添加Question按钮点击，包含JWT验证
  const handleAddQuestionClick = () => {
    if (!validateJWTWithMessage('create a new question')) {
      return;
    }
    setIsAddModalVisible(true);
  };

  const handleAddQuestion = async (questionParams: any) => {
    await fetchAddQuestion(questionParams);
    fetchData();
  };

  const handleEditEvent = async (questionParams: any) => {
    await fetchUpdateQuestion(currentRecord.id, questionParams);
    fetchData();
  };

  const handleCopy = async (record: any) => {
    // 验证JWT
    if (!validateJWTWithMessage('copy this question')) {
      return;
    }

    try {
      const targetRecord = copyData.find((item) => item.id === record.id);
      const timeStamps = dayjs.utc().format('YYYY-MM-DDTHH:mm:ss.SSS+00:00').toString();
      const submitted_by = getLocalStorage('userWallet'); // 基于当前用户钱包地址

      const newSlug = encodeContent(generateUniqueSlug(record.slug));

      // 只复制指定的字段
      const formattedValues = {
        // 时间字段
        created_at: timeStamps,
        end_date: targetRecord.end_date,
        start_date: targetRecord.start_date,
        updated_at: timeStamps,

        // 基本信息字段
        slug: newSlug,
        icon: targetRecord.icon,
        image: targetRecord.image,
        question: encodeContent(targetRecord.question),
        description: encodeContent(targetRecord.description),

        // 交易配置字段
        order_min_size: targetRecord.order_min_size || 1,
        order_price_min_tick_size: targetRecord.order_price_min_tick_size || 0,

        // 状态字段
        active: true,
        closed: false,

        // 用户和条件ID
        submitted_by, // 使用当前用户钱包地址
        condition_id: 'PLACEHOLDER:' + timeStamps,
      };

      // 调用接口
      await fetchAddQuestion(formattedValues);
      await fetchLinkEventQuestion({
        event_id: eventId,
        condition_id: 'PLACEHOLDER:' + timeStamps,
      });
      message.success('Question copied successfully');
      fetchData();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.error || error.response?.data?.message || error.message;
        message.error(`Add failed: ${errorMessage}`);
      }
    }
  };

  const handleExport = async (record: any) => {
    try {
      const targetRecord = copyData.find((item) => item.id === record.id);
      const timeStamps = dayjs.utc().format('YYYY-MM-DDTHH:mm:ss.SSS+00:00').toString();
      const submitted_by = getLocalStorage('userWallet');

      const newSlug = encodeContent(generateUniqueSlug(record.slug));

      // 清理不需要的字段
      const {
        neg_risk_market_id,
        question,
        description,
        id,
        updated_by,
        volume_24hr,
        key,
        ...rest
      } = targetRecord;

      // 格式化数据
      const formattedValues = {
        ...rest,
        slug: newSlug,
        question: encodeContent(question),
        description: encodeContent(description),
        condition_id: 'PLACEHOLDER:' + timeStamps,
        submitted_by,
        created_at: timeStamps,
        updated_at: timeStamps,
        question_id: '',
        active: true,
        closed: false,
      };

      // 导出为 JSON 文件
      const blob = new Blob([JSON.stringify(formattedValues, null, 2)], {
        type: 'application/json',
      });
      saveAs(blob, 'question_data.json'); // 使用 file-saver 保存文件
      message.success('Data has been successfully exported');
    } catch (error) {
      console.error('export failed:', error);
    }
  };

  const handlePublishResultClick = async (record: any) => {
    // 检查用户权限
    const userRole = getCookie('jwt-role');
    const currentWallet = getLocalStorage('userWallet');

    // 如果是event_writer角色，需要检查是否是自己创建的question
    if (userRole === 'event_writer') {
      if (record.submitted_by !== currentWallet) {
        message.error('You can only publish results for questions you created');
        return;
      }
    }
    // back_admin角色可以发布任何question的结果，无需额外检查

    setPublishResultLoading((prev) => ({ ...prev, [record.id]: true }));
    try {
      const response = await handlePublishResult({ id: record.id }, 'outcomeText');
      if (response) {
        message.success('Publish result successful');
        fetchData();
      }
    } catch (error) {
      console.error('Error publishing result:', error);
      message.error('Failed to publish result');
    } finally {
      setPublishResultLoading((prev) => ({ ...prev, [record.id]: false }));
    }
  };

  const columns: ColumnsType<any> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Title',
      dataIndex: 'question',
      key: 'title',
      width: 250,
      fixed: 'left',
      render: (text) => {
        const content = Array.isArray(text) && text.length > 0 ? text[0].content : text;
        return (
          <Tooltip title={content}>
            <Typography.Text strong>
              <div
                style={{
                  maxWidth: 230,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {content}
              </div>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      fixed: 'left',
      render: (icon) => (
        <Image
          src={icon}
          alt="icon"
          width={40}
          height={40}
          style={{ objectFit: 'cover', objectPosition: 'center' }}
        />
      ),
    },
    {
      title: 'Active',
      dataIndex: 'active',
      key: 'active',
      width: 90,
      render: (active) => {
        const status = active ? 'yes' : 'no';
        const text = active ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    {
      title: 'Closed',
      dataIndex: 'closed',
      key: 'closed',
      width: 90,
      render: (closed) => {
        const status = closed ? 'yes' : 'no';
        const text = closed ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    // {
    //   title: 'Updated date',
    //   dataIndex: 'updated_at',
    //   key: 'updated_at',
    //   render: (text) => text && formatDate(text),
    //   sorter: (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
    // },
    {
      title: 'Start Date(UTC)',
      dataIndex: 'start_date',
      key: 'start_date',
      width: 120,
      render: (text) => text && formatDate(text),
    },
    {
      title: 'End Date(UTC)',
      dataIndex: 'end_date',
      key: 'end_date',
      width: 120,
      render: (text) => text && formatDate(text),
    },
    {
      title: 'YES Token',
      dataIndex: 'clob_token_ids',
      key: 'clob_token_ids_yes',
      render: (text) => {
        const content = Array.isArray(text) && text.length > 0 ? text[0] : text;
        return (
          <Tooltip title={content}>
            <Typography.Text strong>
              <div
                style={{
                  maxWidth: 100,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {content}
              </div>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: 'No Token',
      dataIndex: 'clob_token_ids',
      key: 'clob_token_ids_no',
      render: (text) => {
        const content = Array.isArray(text) && text.length > 1 ? text[1] : text;
        return (
          <Tooltip title={content}>
            <Typography.Text strong>
              <div
                style={{
                  maxWidth: 100,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {content}
              </div>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 220,
      fixed: 'right',
      render: (_, record) => {
        const isPublished = record.clob_token_ids && record.clob_token_ids.length > 0;
        const hasOutcome = record.outcome_prices && record.outcome_prices.length > 0;

        // 检查用户是否有权限发布结果
        const userRole = getCookie('jwt-role');
        const currentWallet = getLocalStorage('userWallet');
        const canPublishResult =
          userRole === 'admin' ||
          (userRole === 'event_writer' && record.submitted_by === currentWallet);

        // More 下拉菜单项
        const moreMenuItems: MenuProps['items'] = [
          {
            key: 'copy',
            label: 'Copy',
            onClick: () => handleCopy(record),
          },
          {
            key: 'export',
            label: 'Export',
            onClick: () => handleExport(record),
          },
          ...(!isPublished
            ? [
                {
                  key: 'delete',
                  label: 'Delete',
                  danger: true,
                  onClick: () => {
                    setQuestionId(record.id);
                    setWarningModalType('delete');
                    setIsWarningModalVisible(true);
                  },
                },
              ]
            : []),
        ];

        return (
          <div style={{ display: 'flex', gap: '4px', alignItems: 'center', whiteSpace: 'nowrap' }}>
            <Button
              onClick={() => {
                setCurrentRecord(record);
                setIsEditModalVisible(true);
              }}
            >
              Edit
            </Button>

            {isPublished ? (
              hasOutcome ? (
                <Button disabled>{record.outcome_prices[0] === '1' ? 'YES' : 'NO'}</Button>
              ) : canPublishResult ? (
                <Button
                  type="default"
                  loading={publishResultLoading[record.id] || false}
                  onClick={() => handlePublishResultClick(record)}
                  className="bg-green-600 text-white"
                >
                  Result
                </Button>
              ) : (
                <Button
                  disabled
                  type="default"
                  className="bg-gray-300 text-gray-500"
                  title="You can only publish results for questions you created"
                >
                  No Perm
                </Button>
              )
            ) : (
              <Button
                type="primary"
                key={record.id}
                loading={loading[record.id] || false}
                icon={<PoweroffOutlined />}
                onClick={() => {
                  setCurrentRecord(record);
                  setWarningModalType('publish');
                  setIsWarningModalVisible(true);
                }}
              >
                Publish
              </Button>
            )}

            <Dropdown menu={{ items: moreMenuItems }} trigger={['click']} placement="bottomRight">
              <Button icon={<MoreOutlined />} />
            </Dropdown>
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ overflowX: 'hidden' }}>
      <Breadcrumb url="/#/questions" text={'Question'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card title="Question Table">
            <Space style={{ marginBottom: '24px' }}>
              <Button type="primary" onClick={handleAddQuestionClick}>
                Add Questions
              </Button>
            </Space>
            <div className="table-responsive">
              <Table columns={columns} dataSource={data} pagination={false} scroll={{ x: 1200 }} />
            </div>
          </Card>
        </Col>
      </Row>

      <AddModal
        eventId={eventId}
        setData={setData}
        visible={isAddModalVisible}
        currentEvent={currentEvent}
        setVisibel={setIsAddModalVisible}
        onSubmit={handleAddQuestion}
        onCancel={() => setIsAddModalVisible(false)}
      />
      <EditModal
        visible={isEditModalVisible}
        record={currentRecord}
        onEdit={async (updatedValues) => {
          handleEditEvent(updatedValues);
        }}
        onCancel={() => setIsEditModalVisible(false)}
      />
      <ResultModal
        record={currentRecord}
        loading={isResultLoading}
        setLoading={setIsResultLoading}
        visible={isResultModalVisible}
        fetchData={fetchData}
        onCancel={() => setIsResultModalVisible(false)}
      />
      <WarningModal
        visible={isWarningModalVisible}
        onClose={() => setIsWarningModalVisible(false)}
        eventId={eventId}
        questionId={questionId}
        type={warningModalType}
        fetchData={fetchData}
        currentRecord={currentRecord}
        setLoading={setLoading}
      />
    </div>
  );
};

export default ManageQuestions;
