import React, { useState } from 'react';
import { Button, Input, Space, message, Divider } from 'antd';
import handlePublishResult from '@/utils/signature/createPublishResult';
import CreatInviteCode from './CreatInviteCode';
import CreatBot from './CreatBot';
import AddPromotion from './AddPromotion';
import { getCookie } from '@/utils';
import SearchQuestions from './SearchQuestions';

const jwtRole = getCookie('jwt-role');

const PublishResult: React.FC = () => {
  const [inputQuestionId, setInputQuestionId] = useState<number | undefined>(undefined); // 用于存储输入的 Question ID
  const [publishLoading, setPublishLoading] = useState<boolean>(false);

  const handlePublish = async () => {
    if (inputQuestionId) {
      setPublishLoading(true);
      try {
        const response = await handlePublishResult({ id: inputQuestionId }, 'outcomeText');
        if (response) {
          message.success('Publish successful');
        }
      } catch (error) {
        console.error('Error publishing result:', error);
      } finally {
        setPublishLoading(false);
      }
    }
  };

  return (
    <div className="flex flex-col p-5">
      {jwtRole === 'admin' && (
        <div className="flex flex-col">
          <div className="text-lg font-semibold mb-4">1. Publish Result</div>
          <Space direction="horizontal">
            <label>Question ID：</label>
            <Input
              placeholder="Enter Question ID"
              value={inputQuestionId}
              onChange={(e) => setInputQuestionId(Number(e.target.value))}
            />
            <Button type="primary" onClick={handlePublish} loading={publishLoading}>
              Publish Result
            </Button>
          </Space>
        </div>
      )}

      {jwtRole === 'admin' && (
        <>
          <Divider style={{ margin: '24px 0' }} />
          <SearchQuestions />
          <Divider style={{ margin: '24px 0' }} />
          <CreatInviteCode />
          <Divider style={{ margin: '24px 0' }} />
          <CreatBot />
          <Divider style={{ margin: '24px 0' }} />
          <AddPromotion />
        </>
      )}
    </div>
  );
};

export default PublishResult;
