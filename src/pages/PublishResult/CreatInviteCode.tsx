import React, { useState } from 'react';
import { Button, Input, Space, message } from 'antd';
import dayjs from 'dayjs';
import {
  fetchGetWebUsers,
  fetchUpdateInvitationCode,
  fetchCheckInviteDuplicate,
} from '@/api/publish';
import { v4 as uuidv4 } from 'uuid';

const CreatInviteCode: React.FC = () => {
  const [proxyWallet, setProxyWallet] = useState<string>('');
  const [invitationCode, setInvitationCode] = useState<string | null>(null);

  const handleQueryOrCreate = async () => {
    if (!proxyWallet) {
      message.error('Please enter a Proxy Wallet!');
      return;
    }
    setInvitationCode(null);
    try {
      const response = await fetchGetWebUsers(proxyWallet);

      if (response) {
        const user = response;
        if (user.invitation_code) {
          setInvitationCode(user.invitation_code);
          message.success('Invitation code found!');
        } else {
          let newInvitationCode = uuidv4().split('-')[0]; // 生成一个新的邀请代码，格式为 UUID 的前 8 位
          const isDuplicate = await fetchCheckInviteDuplicate(newInvitationCode);
          if (isDuplicate) {
            newInvitationCode = `${newInvitationCode}-${dayjs.utc().valueOf()}`;
          }

          await fetchUpdateInvitationCode(proxyWallet, newInvitationCode, setInvitationCode);
        }
      } else {
        message.error('Not found user!');
      }
    } catch (error) {
      message.error('Failed to process the request.');
      console.error(error);
    }
  };

  return (
    <div className="flex flex-col">
      <div className="text-lg font-semibold mb-4">3. Generate invitation code</div>
      <div className="flex">
        <Space direction="horizontal">
          <label>Proxy Wallet：</label>
          <Input
            className="w-[400px]"
            placeholder="Enter Proxy Wallet"
            value={proxyWallet}
            onChange={(e) => setProxyWallet(e.target.value)}
          />
          <Button type="primary" onClick={handleQueryOrCreate}>
            Query or Create
          </Button>
        </Space>
      </div>

      {invitationCode && (
        <div className="flex mt-4">
          <div>
            <div className="font-bold">Invitation Link:</div>
            <span>{`https://predict.one?invitationCode=${invitationCode}`}</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default CreatInviteCode;
