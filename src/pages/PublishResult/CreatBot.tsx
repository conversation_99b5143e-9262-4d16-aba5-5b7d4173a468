import React, { useState } from 'react';
import { Button, Input, Space, message, DatePicker, Form } from 'antd';
import axios from 'axios';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

export async function fetchGenerateConfig(payload: any) {
  try {
    const response = await axios({
      url: 'https://cfg.predict.one/generate',
      method: 'post',
      data: payload,
      responseType: 'blob',
    });

    return response;
  } catch (error) {
    console.error('Error generating file:', error);
    throw error;
  }
}

const CreatBot: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    const { name, question_id, poly_token_yes, poly_token_no, end_time } = values;
    const payload = {
      name,
      datacenter: 'dc_ben',
      question_id,
      poly_token_yes,
      poly_token_no,
      end_time: dayjs(end_time).format('YYYY-MM-DD HH:mm:ss'),
      bot_proxywallet: '******************************************',
      bot_proxywallet_volume: '******************************************',
    };

    setLoading(true);
    try {
      const response = await fetchGenerateConfig(payload);

      const reader = new FileReader();
      reader.onload = function () {
        try {
          const decodedResponse = JSON.parse(reader.result as string);
          const decodedTemplate = atob(decodedResponse.template);
          const blob = new Blob([decodedTemplate], { type: 'application/octet-stream' });
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(blob);
          link.download = `${name}.yml`;
          link.click();
          message.success('File downloaded successfully');
        } catch (error) {
          message.error('Failed to process the file. Please try again.');
        }
      };
      reader.readAsText(response.data);
    } catch (error) {
      message.error('Failed to generate file. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <div className="text-lg font-semibold mb-4">4. Download robot configuration</div>
      <Form layout="vertical" onFinish={handleSubmit} className="w-[500px]">
        <Form.Item
          label="Project Name"
          name="name"
          rules={[{ required: true, message: 'Please enter the project name' }]}
        >
          <Input placeholder="Enter project name" />
        </Form.Item>
        <Form.Item
          label="Predict Question ID"
          name="question_id"
          rules={[{ required: true, message: 'Please enter the Predict Question ID' }]}
        >
          <Input placeholder="Enter Predict Question ID" />
        </Form.Item>
        <Form.Item
          label="Poly YES Token ID"
          name="poly_token_yes"
          rules={[{ required: true, message: 'Please enter the Poly YES Token ID' }]}
        >
          <Input placeholder="Enter Poly YES Token ID" />
        </Form.Item>
        <Form.Item
          label="Poly NO Token ID"
          name="poly_token_no"
          rules={[{ required: true, message: 'Please enter the Poly NO Token ID' }]}
        >
          <Input placeholder="Enter Poly NO Token ID" />
        </Form.Item>
        <Form.Item
          label="End Time (UTC)"
          name="end_time"
          rules={[{ required: true, message: 'Please select the end time' }]}
        >
          <DatePicker showTime format="YYYY-MM-DDTHH:mm:ss" />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              Submit and Download
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </div>
  );
};

export default CreatBot;
