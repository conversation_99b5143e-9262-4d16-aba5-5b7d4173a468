import React, { useState } from 'react';
import <PERSON> from 'papaparse';
import { fetchAddPromotion, fetchAllPromotions } from '@/api/publish'; // Import APIs
import { computeProxyAddresses } from '@/contracts/computeProxyAddress'; // Import computeProxyAddresses
import { Button, Space, Result, Upload, Table } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';

const AddPromotion: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [uploadStatus, setUploadStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessages, setErrorMessages] = useState<string[]>([]);
  const [previewData, setPreviewData] = useState<any[]>([]); // Stores preview data
  const [columns, setColumns] = useState<any[]>([]); // Stores table columns
  const [isUploading, setIsUploading] = useState(false); // Controls loading state for the upload button

  const parseCSV = (selectedFile: File) => {
    Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true,
      complete: async (results) => {
        const dataWithId = results.data.map((row: any, index: number) => ({
          id: index + 1, // Add an ID column to indicate the row number
          ...row,
        }));

        // Pre-check: Validate file format
        const formatErrors: string[] = [];
        dataWithId.forEach((row, index) => {
          if (typeof row.owner !== 'string' || !/^0x[a-fA-F0-9]{40}$/.test(row.owner)) {
            formatErrors.push(
              `Row ${index + 1}: Invalid owner (${row.owner}). It must be a 40-character hexadecimal string starting with 0x.`
            );
          }
          if (isNaN(parseFloat(row.amount))) {
            formatErrors.push(`Row ${index + 1}: Invalid amount (${row.amount})`);
          }
        });

        if (formatErrors.length > 0) {
          setErrorMessages(formatErrors);
          setUploadStatus('error');
          return;
        }

        // Batch compute proxy wallets
        const owners = dataWithId.map((row) => row.owner);
        try {
          const proxyWallets = await computeProxyAddresses(owners);
          dataWithId.forEach((row, index) => {
            row.proxy_wallet = proxyWallets[index]; // Add proxy_wallet to each row
          });
        } catch (error) {
          console.error('Error computing proxy wallets:', error);
          setErrorMessages(['Failed to compute proxy wallets. Please try again.']);
          setUploadStatus('error');
          return;
        }

        setPreviewData(dataWithId);

        const dynamicColumns = results.meta.fields
          ? results.meta.fields.map((field) => ({
              title: field,
              dataIndex: field,
              key: field,
            }))
          : [];

        // Add ID and proxy_wallet columns to table definition
        setColumns([
          { title: 'ID', dataIndex: 'id', key: 'id' },
          ...dynamicColumns,
          { title: 'Proxy Wallet', dataIndex: 'proxy_wallet', key: 'proxy_wallet' },
        ]);
      },
    });
  };

  // Upload component properties
  const uploadProps: UploadProps = {
    beforeUpload: () => false, // Prevent automatic upload
    accept: '.csv',
    maxCount: 1,
    listType: 'text',
    onChange(info) {
      const { file, fileList } = info;

      if (fileList.length === 0) {
        // Clear all states if the file list is empty
        console.log('File list is empty, clearing state');
        setFile(null);
        setPreviewData([]);
        setColumns([]);
        setErrorMessages([]);
        setUploadStatus('idle');
        return;
      }

      if (file) {
        const selectedFile = (file.originFileObj as File) || (file as unknown as File);
        setFile(selectedFile);
        setUploadStatus('idle'); // Reset status
        setErrorMessages([]);
        parseCSV(selectedFile); // Parse the CSV file
      }
    },
    onRemove() {
      // Clear related states when the file is removed
      setFile(null); // Clear file cache
      setPreviewData([]); // Clear preview data
      setColumns([]); // Clear table columns
      setErrorMessages([]); // Clear error messages
      setUploadStatus('idle'); // Reset status
    },
  };

  // Upload data to the backend
  const handleUpload = async () => {
    if (!file) return;

    setIsUploading(true); // Set loading state when upload starts

    try {
      // Fetch all existing proxy_wallet data
      const existingPromotions = await fetchAllPromotions();
      const existingWallets = new Set(existingPromotions.map((item: any) => item.owner));

      Papa.parse(file, {
        header: true,
        skipEmptyLines: true,
        complete: async (results) => {
          const data = results.data;
          const duplicateRows: { row: number; owner: string }[] = []; // Store duplicate rows

          // Validate each row
          const validData = data.filter((row: any, index: number) => {
            let isValid = true;

            // Check owner format
            if (typeof row.owner !== 'string' || !/^0x[a-fA-F0-9]{40}$/.test(row.owner)) {
              isValid = false;
            }

            // Check if amount is a valid number
            if (isNaN(parseFloat(row.amount))) {
              isValid = false;
            }

            // Check for duplicates
            if (existingWallets.has(row.owner)) {
              isValid = false;
              duplicateRows.push({ row: index + 1, owner: row.owner }); // Record duplicate rows
            }

            return isValid;
          });

          // Compute proxy wallets for valid data
          const owners = validData.map((row: any) => row.owner);
          try {
            const proxyWallets = await computeProxyAddresses(owners);
            validData.forEach((row: any, index: number) => {
              row.proxy_wallet = proxyWallets[index]; // Add proxy_wallet to each row
            });
          } catch (error) {
            console.error('Error computing proxy wallets:', error);
            setErrorMessages(['Failed to compute proxy wallets. Please try again.']);
            setUploadStatus('error');
            setIsUploading(false); // Cancel loading state after upload
            return;
          }

          // Process each row's amount by multiplying it by 10^6
          validData.forEach((row: any) => {
            row.amount = parseFloat(row.amount) * Math.pow(10, 6);
          });

          if (duplicateRows.length > 0) {
            // Add detailed duplicate row information to error messages
            const duplicateErrors = duplicateRows.map(
              ({ row, owner }) => `Row ${row}: Duplicate owner (${owner})`
            );
            setErrorMessages(duplicateErrors);
            setUploadStatus('error');
            setIsUploading(false); // Cancel loading state after upload
            return;
          }

          try {
            const affectedRows = await fetchAddPromotion(
              validData.map((row: any) => ({
                owner: row.owner,
                proxy_wallet: row.proxy_wallet,
                amount: parseFloat(row.amount),
              }))
            );
            console.log('Inserted rows:', affectedRows);
            setUploadStatus('success');
          } catch (error: any) {
            console.error('Error inserting promotions:', error);
            setErrorMessages(['An unknown error occurred. Please try again.']);
            setUploadStatus('error');
          } finally {
            setIsUploading(false); // Cancel loading state after upload
          }
        },
      });
    } catch (error) {
      console.error('Error fetching existing promotions:', error);
      setErrorMessages(['Failed to fetch existing promotions. Please try again.']);
      setUploadStatus('error');
      setIsUploading(false); // Cancel loading state after upload
    }
  };

  const handleRetry = () => {
    setFile(null); // Clear file cache
    setUploadStatus('idle'); // Reset status
    setErrorMessages([]); // Clear error messages
    setPreviewData([]); // Clear preview data
    setColumns([]); // Clear table columns
  };

  return (
    <div className="flex flex-col">
      <div className="text-lg font-semibold mb-4">
        5. Upload CSV File to Promotion Table{' '}
        <a
          href="/sample-demo.csv"
          download="sample-demo.csv"
          style={{ fontSize: '16px', color: '#1890ff' }}
        >
          (Download Template)
        </a>
      </div>
      {uploadStatus === 'idle' && (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Upload {...uploadProps}>
            <Button icon={<UploadOutlined />}>Select File</Button>
          </Upload>
          {previewData.length > 0 && (
            <Table
              dataSource={previewData}
              columns={columns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
              style={{ marginTop: 16 }}
            />
          )}
          <Button type="primary" onClick={handleUpload} disabled={!file} loading={isUploading}>
            Upload
          </Button>
        </Space>
      )}
      {uploadStatus === 'success' && (
        <Result
          status="success"
          title="Upload Successful"
          subTitle="The CSV file has been successfully uploaded and inserted into the Promotion table."
          extra={[
            <Button
              type="primary"
              onClick={() => {
                setUploadStatus('idle');
                handleRetry();
              }}
              key="upload-again"
            >
              Upload Again
            </Button>,
          ]}
        />
      )}
      {uploadStatus === 'error' && (
        <Result
          status="error"
          title="Upload Failed"
          subTitle="The following rows are invalid. Please fix them and try again:"
          extra={[
            <div key="error-messages">
              {errorMessages.map((msg, index) => (
                <p key={index} style={{ color: 'red', margin: 0 }}>
                  {msg}
                </p>
              ))}
            </div>,
            <Button type="primary" onClick={handleRetry} key="retry">
              Retry
            </Button>,
          ]}
        />
      )}
    </div>
  );
};

export default AddPromotion;
