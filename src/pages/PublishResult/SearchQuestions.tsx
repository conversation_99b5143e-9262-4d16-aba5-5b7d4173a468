import React, { useState } from 'react';
import { Input, Button, Space, Table, message, Divider, Tooltip, Typography } from 'antd';
import { fetchGetQuestionByTokenId, fetchGetQuestionByQuestionId } from '@/api/publish';
import { formatDate } from '@/utils';

const renderWithTooltip = (content: string | undefined, maxWidth = 200) => (
  <Tooltip title={content}>
    <Typography.Text strong>
      <div
        style={{
          maxWidth,
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
        }}
      >
        {content || 'No Data'}
      </div>
    </Typography.Text>
  </Tooltip>
);

const decodeQuestionData = (data: any) => {
  if (!Array.isArray(data)) {
    console.error('decodeQuestionData expects an array, but received:', data);
    return [];
  }

  return data.map((item) => {
    try {
      return {
        ...item,
        slug: JSON.parse(atob(item.slug)),
        question: JSON.parse(atob(item.question)),
      };
    } catch (error) {
      console.error('Error decoding item:', item, error);
      return item;
    }
  });
};

const SearchQuestions: React.FC = () => {
  // 状态：通过 Token ID 查询
  const [inputTokenId, setInputTokenId] = useState<string>(''); // Token ID 输入
  const [questionDataByTokenId, setQuestionDataByTokenId] = useState<any>(null); // 查询结果
  const [loadingByTokenId, setLoadingByTokenId] = useState<boolean>(false); // 加载状态

  // 状态：通过 Question ID 查询
  const [inputQuestionId, setInputQuestionId] = useState<number | undefined>(undefined); // Question ID 输入
  const [questionDataByQuestionId, setQuestionDataByQuestionId] = useState<any>(null); // 查询结果
  const [loadingByQuestionId, setLoadingByQuestionId] = useState<boolean>(false); // 加载状态

  const decodeDataByTokenId = questionDataByTokenId && decodeQuestionData(questionDataByTokenId);
  const decodeDataByQuestionId =
    questionDataByQuestionId && decodeQuestionData(questionDataByQuestionId);

  const columns = [
    { title: 'ID', dataIndex: 'id', key: 'id' },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug',
      render: (slug: any) => {
        if (Array.isArray(slug)) {
          const enSlug = slug.find((item: { language: string }) => item.language === 'en');
          return renderWithTooltip(enSlug?.content);
        }
        return renderWithTooltip('Invalid Slug Data');
      },
    },
    {
      title: 'Question',
      dataIndex: 'question',
      key: 'question',
      render: (question: any) => {
        if (Array.isArray(question)) {
          const enQuestion = question.find((item: { language: string }) => item.language === 'en');
          return renderWithTooltip(enQuestion?.content);
        }
        return renderWithTooltip('Invalid Question Data');
      },
    },
    {
      title: 'Condition ID',
      dataIndex: 'condition_id',
      key: 'condition_id',
      render: (conditionId: string) => renderWithTooltip(conditionId, 100),
    },
    {
      title: 'Active',
      dataIndex: 'active',
      key: 'active',
      render: (active: boolean) => (active ? 'Yes' : 'No'),
    },
    {
      title: 'Closed',
      dataIndex: 'closed',
      key: 'closed',
      render: (closed: boolean) => (closed ? 'Yes' : 'No'),
    },
    {
      title: 'YES Token',
      dataIndex: 'clob_token_ids',
      key: 'clob_token_ids_yes',
      render: (text: any) => {
        const content = Array.isArray(text) && text.length > 0 ? text[0] : 'No Data';
        return renderWithTooltip(content, 100);
      },
    },
    {
      title: 'No Token',
      dataIndex: 'clob_token_ids',
      key: 'clob_token_ids_no',
      render: (text: any) => {
        const content = Array.isArray(text) && text.length > 1 ? text[1] : 'No Data';
        return renderWithTooltip(content, 100);
      },
    },
    {
      title: 'Start Date(UTC)',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text: any) => text && formatDate(text),
    },
    {
      title: 'End Date(UTC)',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text: any) => text && formatDate(text),
    },
    {
      title: 'Volume',
      dataIndex: 'volumeclob',
      key: 'volumeclob',
    },
  ];

  const handleSearchByTokenId = async () => {
    if (!inputTokenId) {
      message.error('Please enter a valid Token ID');
      return;
    }

    setLoadingByTokenId(true);
    try {
      const result = await fetchGetQuestionByTokenId(inputTokenId);
      setQuestionDataByTokenId(result.data.question_market);
      message.success('Query successful');
    } catch (error) {
      console.error('Error fetching question by Token ID:', error);
      message.error('Failed to fetch question. Please try again.');
    } finally {
      setLoadingByTokenId(false);
    }
  };

  const handleSearchByQuestionId = async () => {
    if (!inputQuestionId) {
      message.error('Please enter a valid Question ID');
      return;
    }

    setLoadingByQuestionId(true);
    try {
      const result = await fetchGetQuestionByQuestionId(inputQuestionId.toString());
      setQuestionDataByQuestionId(result.data.question_market);
      message.success('Query successful');
    } catch (error) {
      console.error('Error fetching question by Question ID:', error);
      message.error('Failed to fetch question. Please try again.');
    } finally {
      setLoadingByQuestionId(false);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <div>
        <div className="text-lg font-semibold mb-2">2.1 Search Question by Token id</div>
        <Space direction="horizontal">
          <label>Token ID：</label>
          <Input
            placeholder="Enter Token ID"
            value={inputTokenId}
            onChange={(e) => setInputTokenId(e.target.value)}
          />
          <Button type="primary" onClick={handleSearchByTokenId} loading={loadingByTokenId}>
            Search
          </Button>
        </Space>
        <div className="flex mt-4">
          {questionDataByTokenId ? (
            <Table
              dataSource={decodeDataByTokenId}
              columns={columns}
              rowKey="id"
              bordered
              loading={loadingByTokenId}
              pagination={false}
            />
          ) : (
            <div>No data to display</div>
          )}
        </div>
      </div>

      <Divider style={{ margin: '8px 0' }} />

      <div>
        <div className="text-lg font-semibold mb-2">2.2 Search Question by Question id</div>
        <Space direction="horizontal">
          <label>Question ID：</label>
          <Input
            placeholder="Enter Question ID"
            value={inputQuestionId}
            onChange={(e) => setInputQuestionId(Number(e.target.value))}
          />
          <Button type="primary" onClick={handleSearchByQuestionId} loading={loadingByQuestionId}>
            Search
          </Button>
        </Space>
        <div className="flex mt-4">
          {questionDataByQuestionId ? (
            <Table
              dataSource={decodeDataByQuestionId}
              columns={columns}
              rowKey="id"
              bordered
              loading={loadingByQuestionId}
              pagination={false}
            />
          ) : (
            <div>No data to display</div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchQuestions;
