import React, { useState, useEffect, useCallback } from 'react';
import { Button, App } from 'antd';
import { useNavigate } from 'react-router-dom';
import { handleLogin } from '@/utils/signature/loginSignature';
import 'tailwindcss/tailwind.css';

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { message } = App.useApp();

  useEffect(() => {
    const checkMetaMask = async () => {
      if (window.ethereum) {
        console.log('MetaMask is installed!');
      } else {
        message.error('MetaMask is not installed. Please install MetaMask and try again.');
      }
    };
    checkMetaMask();
  }, [message]);

  const handleLoginClick = useCallback(() => {
    handleLogin(setLoading, message, navigate);
  }, [message, navigate]);

  const renderMetaMaskButton = () => (
    <Button
      type="primary"
      block
      size="large"
      onClick={handleLoginClick}
      loading={loading}
      className="mt-4 bg-gradient-to-r from-blue-500 to-purple-700 border-none text-sm md:text-lg py-2 md:py-4"
    >
      Login with MetaMask
    </Button>
  );

  return (
    <div className="flex items-center justify-center">
      <div className="w-[90%] h-[80%] flex items-center justify-center relative">
        <img
          src="/login-back.jpg"
          alt="Login-Bg-Image"
          className="object-cover object-right overflow-hidden rounded-2xl shadow-2xl w-full h-full md:w-auto md:h-auto"
        />

        <div className="w-full md:w-auto sm:right-8 flex flex-col items-center md:items-start justify-center p-4 md:p-8 bg-gray-500 bg-opacity-20 rounded-xl shadow-xl absolute max-w-[90%] max-h-[80%]">
          <div className="flex flex-col items-center md:items-start justify-center mb-2">
            <h1 className="text-3xl md:text-5xl font-bold text-gray-200 mb-2 md:mb-4">Sign In</h1>
            <div className="flex items-center justify-center mb-2">
              <img
                src="/logo.png"
                alt="logo"
                className="size-8 md:size-10 rounded-full left-2 top-2"
              />
              <div className="text-xl md:text-3xl ml-2 font-bold bg-clip-text bg-gradient-to-br from-blue-500 to-purple-700 text-transparent">
                PredictOne
              </div>
            </div>
            <p className="text-lg md:text-xl font-bold text-gray-200">Create Your Own</p>
            <div className="text-lg md:text-xl font-bold text-gray-200 ml-4 md:ml-12 bg-clip-text bg-gradient-to-br from-blue-500 to-purple-700 text-transparent">
              <p className="">DECENTRALIZED</p>
              <p className="">PREDICTION MARKET</p>
            </div>
          </div>
          {renderMetaMaskButton()}
        </div>
      </div>
    </div>
  );
};

export default Login;
