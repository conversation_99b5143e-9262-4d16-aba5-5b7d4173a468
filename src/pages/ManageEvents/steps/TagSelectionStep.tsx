import React, { useState, useEffect, useCallback } from 'react';
import { Form, Select, FormInstance, Button } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { fetchTagsListByRegion } from '@/api/tags';

interface TagSelectionStepProps {
  form: FormInstance;
  stepData: any;
  selectedTagIdList: string[];
  setSelectedTagIdList: (tags: string[]) => void;
  userRole?: string;
}

const TagSelectionStep: React.FC<TagSelectionStepProps> = ({
  form,
  stepData,
  selectedTagIdList,
  setSelectedTagIdList,
  userRole,
}) => {
  const [options, setOptions] = useState<{ value: string; label: string; id: number }[]>([]);
  const [loading, setLoading] = useState(false);
  const [creatorTagId, setCreatorTagId] = useState<number | null>(null);

  // 获取标签列表
  const searchTags = useCallback(
    async (languages: string[], isInitialLoad = false) => {
      if (!languages || languages.length === 0) return;

      setLoading(true);
      try {
        const response = await fetchTagsListByRegion(languages);
        const seenLabels = new Set();

        const newOptions = response.data.tags
          .map((item: { id: number; label: string }) => ({
            value: item.label,
            label: item.label,
            id: item.id,
          }))
          .filter((option: any) => {
            if (seenLabels.has(option.label)) {
              return false;
            } else {
              seenLabels.add(option.label);
              return true;
            }
          });

        setOptions(newOptions);

        // 查找creator标签ID
        const creatorTag = response.data.tags.find((tag: any) => tag.slug === 'creator');
        if (creatorTag) {
          setCreatorTagId(creatorTag.id);

          // 如果是event_writer角色，自动添加creator标签（只在初始加载且没有已选标签时）
          if (
            userRole === 'event_writer' &&
            isInitialLoad &&
            selectedTagIdList.length === 0 &&
            (!stepData.tags || stepData.tags.length === 0)
          ) {
            const creatorOption = newOptions.find((opt: any) => opt.id === creatorTag.id);
            if (creatorOption) {
              setSelectedTagIdList([creatorTag.id.toString()]);
              form.setFieldsValue({ tags: [creatorOption.value] });
            }
          }
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setLoading(false);
      }
    },
    [userRole, form]
  );

  // 当语言改变时重新获取标签
  useEffect(() => {
    const languages = stepData.languages || form.getFieldValue('languages');
    if (languages && languages.length > 0) {
      searchTags(languages, false);
    }
  }, [stepData.languages, searchTags]);

  // 确保在组件挂载时也获取标签（解决后退再前进的问题）
  useEffect(() => {
    const languages = stepData.languages || form.getFieldValue('languages');
    if (languages && languages.length > 0 && options.length === 0) {
      searchTags(languages, true);
    }
  }, [searchTags]);

  // 初始化已选择的标签 - 简化逻辑避免循环依赖
  useEffect(() => {
    if (options.length === 0) return; // 等待options加载完成

    // 优先使用stepData.tags，如果没有则使用selectedTagIdList
    const tagsToUse = stepData.tags && stepData.tags.length > 0 ? stepData.tags : selectedTagIdList;

    if (tagsToUse && tagsToUse.length > 0) {
      // 将tag ID转换为tag值
      const tagValues = tagsToUse
        .map((tagId: string) => options.find((opt) => opt.id.toString() === tagId)?.value)
        .filter((value: any): value is string => Boolean(value));

      // 获取当前表单值
      const currentFormValues = form.getFieldValue('tags') || [];
      const currentValuesStr = currentFormValues.sort().join(',');
      const newValuesStr = tagValues.sort().join(',');

      // 只有当表单值确实需要更新时才更新
      if (currentValuesStr !== newValuesStr) {
        form.setFieldsValue({ tags: tagValues });
      }

      // 同步selectedTagIdList（只有当stepData.tags存在时）
      if (stepData.tags && stepData.tags.length > 0) {
        const currentIds = selectedTagIdList.sort().join(',');
        const stepIds = stepData.tags.sort().join(',');
        if (currentIds !== stepIds) {
          setSelectedTagIdList([...stepData.tags]);
        }
      }
    }
  }, [options, stepData.tags]); // 移除form和selectedTagIdList依赖

  // 处理标签选择变化
  const handleTagChange = (selectedValues: string[]) => {
    // 如果options还没加载完成，先保存选择的值，等options加载完成后再处理
    if (options.length === 0) {
      form.setFieldsValue({ tags: selectedValues });
      return;
    }

    const matchedIds: string[] = selectedValues
      .map((selectedValue: string) => {
        const option = options.find((opt: any) => opt.value === selectedValue);
        return option ? option.id.toString() : null;
      })
      .filter((id): id is string => id !== null);

    // 为event_writer角色确保creator标签始终被选中
    let finalIds: string[] = [...new Set(matchedIds)];
    let finalValues: string[] = [...selectedValues];

    if (userRole === 'event_writer' && creatorTagId) {
      const creatorIdStr = creatorTagId.toString();
      if (!finalIds.includes(creatorIdStr)) {
        finalIds.push(creatorIdStr);
      }

      // 同时更新表单值，确保creator标签在UI中也显示
      const creatorOption = options.find((opt) => opt.id === creatorTagId);
      if (creatorOption && !finalValues.includes(creatorOption.value)) {
        finalValues.push(creatorOption.value);
      }
    }

    // 更新表单值
    form.setFieldsValue({ tags: finalValues });
    setSelectedTagIdList(Array.from(new Set(finalIds)));
  };

  // 处理标签删除
  const handleDeselect = (value: string) => {
    // 获取当前表单值
    const currentValues = form.getFieldValue('tags') || [];

    // 如果options还没加载完成，直接从表单值中删除
    if (options.length === 0) {
      const updatedValues = currentValues.filter((v: string) => v !== value);
      form.setFieldsValue({ tags: updatedValues });
      return;
    }

    if (userRole === 'event_writer' && creatorTagId) {
      const option = options.find((opt) => opt.value === value);
      if (option?.id === creatorTagId) {
        // 阻止删除creator标签，重新添加
        setTimeout(() => {
          const latestValues = form.getFieldValue('tags') || [];
          if (!latestValues.includes(value)) {
            const newValues = [...latestValues, value];
            form.setFieldsValue({ tags: newValues });
          }
        }, 0);
        return; // 不执行后续的删除逻辑
      }
    }

    // 处理普通标签删除 - 更新selectedTagIdList
    const updatedValues = currentValues.filter((v: string) => v !== value);
    const updatedIds = updatedValues
      .map((selectedValue: string) => {
        const option = options.find((opt: any) => opt.value === selectedValue);
        return option ? option.id.toString() : null;
      })
      .filter((id: string | null): id is string => id !== null);

    setSelectedTagIdList(updatedIds);
  };

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes slideInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}
      </style>

      <div style={{ width: '96%', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #10b981, #059669)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(59, 130, 246, 0.3)',
              }}
            >
              🏷️
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  letterSpacing: '0.4px',
                }}
              >
                Tag Selection
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                Choose relevant categories for your event
              </div>
            </div>
          </div>

          {/* 表单内容 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '32px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {/* 标签选择 */}
            <div>
              {/* 标题和刷新按钮 - 独立布局 */}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  width: '100%',
                  marginBottom: '16px',
                }}
              >
                <span
                  style={{
                    fontSize: '24px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  🏷️ Tags
                </span>
                <Button
                  type="primary"
                  icon={<ReloadOutlined spin={loading} />}
                  loading={loading}
                  onClick={() => {
                    const languages = stepData.languages || form.getFieldValue('languages');
                    if (languages && languages.length > 0) {
                      searchTags(languages, false);
                    }
                  }}
                  style={{
                    background: loading
                      ? 'linear-gradient(135deg, #94a3b8 0%, #64748b 100%)'
                      : 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                    border: 'none',
                    borderRadius: '8px',
                    fontSize: '13px',
                    fontWeight: '600',
                    height: '32px',
                    padding: '0 12px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '6px',
                    boxShadow: loading
                      ? '0 2px 4px rgba(148, 163, 184, 0.2)'
                      : '0 2px 8px rgba(59, 130, 246, 0.3)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    transform: 'translateY(0)',
                  }}
                  onMouseEnter={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(-1px)';
                      e.currentTarget.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.4)';
                      e.currentTarget.style.background =
                        'linear-gradient(135deg, #2563eb 0%, #1e40af 100%)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!loading) {
                      e.currentTarget.style.transform = 'translateY(0)';
                      e.currentTarget.style.boxShadow = '0 2px 8px rgba(59, 130, 246, 0.3)';
                      e.currentTarget.style.background =
                        'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)';
                    }
                  }}
                  title="Click to refresh tags list if you just created a new tag"
                >
                  {loading ? 'Refreshing...' : 'Refresh Tags'}
                </Button>
              </div>

              <Form.Item
                name="tags"
                style={{ marginBottom: '16px' }}
                rules={[
                  { required: true, message: 'Select at least one tag' },
                  ...(userRole === 'event_writer'
                    ? [
                        {
                          validator: (_: any, value: string[]) => {
                            if (!value || value.length === 0) {
                              return Promise.reject(new Error('Select at least one tag'));
                            }

                            // 检查除了creator标签外是否还有其他标签
                            const creatorOption = options.find((opt) => opt.id === creatorTagId);
                            const nonCreatorTags = value.filter(
                              (tag) => tag !== creatorOption?.value
                            );

                            if (nonCreatorTags.length === 0) {
                              return Promise.reject(
                                new Error('Select at least one additional tag')
                              );
                            }

                            return Promise.resolve();
                          },
                        },
                      ]
                    : []),
                ]}
              >
                <Select
                  mode="multiple"
                  allowClear
                  style={{
                    width: '100%',
                    fontSize: '18px',
                    borderRadius: '12px',
                  }}
                  size="large"
                  placeholder={
                    userRole === 'event_writer'
                      ? 'Please select tags (creator tag is required + at least 1 more)'
                      : 'Please select Tags'
                  }
                  loading={loading}
                  onChange={handleTagChange}
                  onDeselect={handleDeselect}
                  options={options.map((option) => ({
                    ...option,
                    // 为event_writer角色的creator标签添加特殊标识
                    label:
                      userRole === 'event_writer' && option.id === creatorTagId
                        ? `${option.label}`
                        : option.label,
                  }))}
                  tagRender={(props) => {
                    const { label, value, closable, onClose } = props;
                    const option = options.find((opt) => opt.value === value);
                    const isCreatorTag = userRole === 'event_writer' && option?.id === creatorTagId;

                    return (
                      <span
                        style={{
                          display: 'inline-block',
                          padding: '8px 14px',
                          margin: '3px',
                          background: isCreatorTag
                            ? 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)'
                            : 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                          border: isCreatorTag ? '2px solid #d1d5db' : '2px solid #3b82f6',
                          borderRadius: '8px',
                          fontSize: '16px',
                          color: isCreatorTag ? '#6b7280' : 'white',
                          lineHeight: '1.4',
                          fontWeight: '600',
                          boxShadow: isCreatorTag
                            ? '0 2px 4px rgba(0, 0, 0, 0.1)'
                            : '0 3px 6px rgba(59, 130, 246, 0.3)',
                          transition: 'all 0.2s ease',
                        }}
                      >
                        {label}
                        {closable && !isCreatorTag && (
                          <span
                            style={{
                              marginLeft: '8px',
                              cursor: 'pointer',
                              color: 'white',
                              fontSize: '16px',
                              fontWeight: 'bold',
                              width: '18px',
                              height: '18px',
                              display: 'inline-flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              borderRadius: '50%',
                              background: 'rgba(255, 255, 255, 0.2)',
                              transition: 'all 0.2s ease',
                            }}
                            onMouseEnter={(e) => {
                              (e.target as HTMLElement).style.background =
                                'rgba(255, 255, 255, 0.3)';
                            }}
                            onMouseLeave={(e) => {
                              (e.target as HTMLElement).style.background =
                                'rgba(255, 255, 255, 0.2)';
                            }}
                            onClick={(e) => {
                              e.stopPropagation();
                              onClose();
                            }}
                          >
                            ×
                          </span>
                        )}
                        {isCreatorTag && (
                          <span
                            style={{
                              marginLeft: '8px',
                              color: '#9ca3af',
                              fontSize: '12px',
                              fontWeight: 'bold',
                              background: '#f9fafb',
                              padding: '2px 6px',
                              borderRadius: '4px',
                            }}
                          >
                            REQUIRED
                          </span>
                        )}
                      </span>
                    );
                  }}
                />
              </Form.Item>

              <p
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                  fontStyle: 'italic',
                  margin: 0,
                }}
              >
                Choose relevant categories to help users discover your event, Creator tag will be
                added automatically + select at least 1 more
              </p>
            </div>

            {/* 创建新标签区域 */}
            <div
              style={{
                textAlign: 'center',
                padding: '24px',
                background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                borderRadius: '16px',
                border: '2px solid #e2e8f0',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.06)',
                animation: 'slideInUp 0.8s ease-out',
              }}
            >
              <div
                style={{
                  fontSize: '16px',
                  color: '#64748b',
                  marginBottom: '16px',
                  fontWeight: '600',
                }}
              >
                Can't find the tag you need?
              </div>
              <a
                href="/#/tags"
                target="_blank"
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '10px',
                  padding: '14px 28px',
                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                  color: 'white',
                  textDecoration: 'none',
                  borderRadius: '12px',
                  fontSize: '16px',
                  fontWeight: '600',
                  boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
                  transition: 'all 0.2s ease',
                  border: 'none',
                  cursor: 'pointer',
                }}
                onMouseEnter={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(-2px)';
                  target.style.boxShadow = '0 6px 16px rgba(59, 130, 246, 0.4)';
                }}
                onMouseLeave={(e) => {
                  const target = e.target as HTMLElement;
                  target.style.transform = 'translateY(0)';
                  target.style.boxShadow = '0 4px 12px rgba(59, 130, 246, 0.3)';
                }}
              >
                <span
                  style={{
                    fontSize: '18px',
                    fontWeight: 'bold',
                  }}
                >
                  +
                </span>
                Create New Tag
                <span
                  style={{
                    fontSize: '14px',
                    opacity: '0.8',
                  }}
                >
                  ↗
                </span>
              </a>
              <div
                style={{
                  fontSize: '14px',
                  color: '#94a3b8',
                  marginTop: '8px',
                  fontStyle: 'italic',
                }}
              >
                Opens in a new tab
              </div>
              <div
                style={{
                  fontSize: '14px',
                  color: '#94a3b8',
                  marginTop: '4px',
                  fontStyle: 'italic',
                }}
              >
                Click the "Refresh Tags" button above to see it in the list!
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default TagSelectionStep;
