import React, { useEffect, useState } from 'react';
import { Form, Input, FormInstance } from 'antd';

interface ContentStepProps {
  form: FormInstance;
  stepData: any;
}

const ContentStep: React.FC<ContentStepProps> = ({ form, stepData }) => {
  const languages = stepData.languages || form.getFieldValue('languages') || ['en'];

  // 折叠状态管理 - 默认只展开第一个卡片
  const [expandedCards, setExpandedCards] = useState<{ [key: string]: boolean }>(() => {
    const initialState: { [key: string]: boolean } = {};
    languages.forEach((language: string, index: number) => {
      initialState[language] = index === 0; // 只有第一个默认展开
    });
    return initialState;
  });

  // 切换卡片展开/折叠状态
  const toggleCard = (language: string) => {
    setExpandedCards((prev) => ({
      ...prev,
      [language]: !prev[language],
    }));
  };

  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};
    languages.forEach((language: string) => {
      if (stepData[`title_${language}`]) {
        initialValues[`title_${language}`] = stepData[`title_${language}`];
      }
      if (stepData[`multimedia_url_${language}`]) {
        initialValues[`multimedia_url_${language}`] = stepData[`multimedia_url_${language}`];
      }
    });

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [stepData, form, languages]);

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(-10px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          /* 使用全局custom-input样式，这里不需要重复定义 */
        `}
      </style>

      <div style={{ width: '96%', margin: '0 auto' }}>
        {/* 整体表单容器 */}
        <div
          style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
            border: '2px solid #e2e8f0',
            borderRadius: '24px',
            padding: '40px',
            boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
            position: 'relative',
            overflow: 'hidden',
          }}
        >
          {/* 装饰性背景元素 */}
          <div
            style={{
              position: 'absolute',
              top: '-40px',
              right: '-40px',
              width: '120px',
              height: '120px',
              background: 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
              borderRadius: '50%',
              opacity: '0.04',
            }}
          ></div>
          <div
            style={{
              position: 'absolute',
              bottom: '-50px',
              left: '-50px',
              width: '150px',
              height: '150px',
              background: 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
              borderRadius: '50%',
              opacity: '0.03',
            }}
          ></div>

          {/* 表单标题 */}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '16px',
              marginBottom: '20px',
              paddingBottom: '20px',
              borderBottom: '3px solid #e2e8f0',
              position: 'relative',
              zIndex: 1,
            }}
          >
            <div
              style={{
                width: '56px',
                height: '56px',
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                borderRadius: '18px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '24px',
                boxShadow: '0 6px 16px rgba(59, 130, 246, 0.3)',
              }}
            >
              📝
            </div>
            <div>
              <div
                style={{
                  fontSize: '28px',
                  fontWeight: '800',
                  color: '#1f2937',
                  letterSpacing: '0.4px',
                }}
              >
                Event Content
              </div>
              <div
                style={{
                  fontSize: '16px',
                  color: '#6b7280',
                  fontWeight: '500',
                }}
              >
                First language is required, others are optional
              </div>
            </div>
          </div>

          {/* 语言表单列表 */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '32px',
              position: 'relative',
              zIndex: 1,
            }}
          >
            {languages.map((language: string, index: number) => {
              const langName =
                {
                  en: '🇺🇸 English',
                  zh: '🇨🇳 Chinese',
                  ja: '🇯🇵 Japanese',
                  ko: '🇰🇷 Korean',
                }[language] || `🌐 ${language.toUpperCase()}`;

              return (
                <div
                  key={`${language}_${index}`}
                  style={{
                    background: index % 2 === 0 ? '#ffffff' : '#f9fafb',
                    border: '1px solid #e5e7eb',
                    borderRadius: '16px',
                    padding: '28px',
                    borderLeft: `6px solid ${index === 0 ? '#ef4444' : '#3b82f6'}`,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                  }}
                >
                  {/* 语言标题行 - 可点击折叠/展开 */}
                  <div
                    onClick={() => toggleCard(language)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      marginBottom: expandedCards[language] ? '24px' : '0px',
                      paddingBottom: '12px',
                      borderBottom: '1px solid #e5e7eb',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                  >
                    <div
                      style={{
                        fontSize: '24px',
                        width: '40px',
                        height: '40px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: 'linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%)',
                        borderRadius: '12px',
                        boxShadow: '0 2px 6px rgba(0, 0, 0, 0.1)',
                      }}
                    >
                      {langName.split(' ')[0]}
                    </div>
                    <div style={{ flex: 1 }}>
                      <span
                        style={{
                          fontSize: '18px',
                          fontWeight: '700',
                          color: '#1f2937',
                          letterSpacing: '0.3px',
                        }}
                      >
                        {langName.split(' ').slice(1).join(' ')}
                      </span>
                      {index === 0 && (
                        <span
                          style={{
                            marginLeft: '12px',
                            display: 'inline-flex',
                            alignItems: 'center',
                            gap: '4px',
                            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                            color: 'white',
                            padding: '3px 8px',
                            borderRadius: '6px',
                            fontSize: '11px',
                            fontWeight: '600',
                            boxShadow: '0 2px 4px rgba(239, 68, 68, 0.3)',
                          }}
                        >
                          <span>⚡</span>
                          Required
                        </span>
                      )}
                    </div>
                    {/* 折叠/展开图标 */}
                    <div
                      style={{
                        width: '32px',
                        height: '32px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        background: expandedCards[language]
                          ? 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
                          : 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
                        borderRadius: '8px',
                        color: 'white',
                        fontSize: '16px',
                        fontWeight: 'bold',
                        boxShadow: '0 2px 6px rgba(0, 0, 0, 0.15)',
                        transition: 'all 0.2s ease',
                        transform: expandedCards[language] ? 'rotate(180deg)' : 'rotate(0deg)',
                      }}
                    >
                      ▼
                    </div>
                  </div>

                  {/* 表单内容 - 只有展开时才显示 */}
                  {expandedCards[language] && (
                    <div
                      style={{
                        animation: 'fadeIn 0.3s ease-in-out',
                      }}
                    >
                      <div style={{ marginBottom: '32px' }}>
                        <Form.Item
                          name={`title_${language}`}
                          label={
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px',
                              }}
                            >
                              <div
                                style={{
                                  width: '32px',
                                  height: '32px',
                                  background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                                  borderRadius: '10px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                  color: 'white',
                                  fontWeight: 'bold',
                                  boxShadow: '0 3px 8px rgba(16, 185, 129, 0.3)',
                                }}
                              >
                                📝
                              </div>
                              <span
                                style={{
                                  fontSize: '20px',
                                  fontWeight: '700',
                                  color: '#1f2937',
                                  letterSpacing: '0.3px',
                                }}
                              >
                                Title
                              </span>
                            </div>
                          }
                          validateTrigger={['onChange', 'onBlur']}
                          rules={
                            index === 0
                              ? [
                                  {
                                    required: true,
                                    message: 'Title is required',
                                    whitespace: true,
                                  },
                                  { min: 5, message: 'At least 5 characters' },
                                  { max: 100, message: 'Max 100 characters' },
                                ]
                              : [{ max: 100, message: 'Max 100 characters' }]
                          }
                          style={{ marginBottom: '8px' }}
                          className="custom-input"
                        >
                          <Input
                            placeholder="Enter event title..."
                            showCount
                            maxLength={100}
                            size="large"
                            onChange={(e) => {
                              // 确保表单值被正确设置
                              form.setFieldsValue({ [`title_${language}`]: e.target.value });
                            }}
                            style={{
                              fontSize: '16px',
                              borderRadius: '12px',
                              border: '2px solid #e5e7eb',
                              padding: '14px 18px',
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                              transition: 'all 0.2s ease',
                            }}
                          />
                        </Form.Item>
                        <p
                          style={{
                            fontSize: '16px',
                            color: '#6b7280',
                            fontWeight: '500',
                            marginTop: '-4px',
                            fontStyle: 'italic',
                          }}
                        >
                          Clear and engaging title that describes your event
                        </p>
                      </div>

                      <div style={{ marginBottom: '32px' }}>
                        <Form.Item
                          name={`multimedia_url_${language}`}
                          label={
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '12px',
                              }}
                            >
                              <div
                                style={{
                                  width: '32px',
                                  height: '32px',
                                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                  borderRadius: '10px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  fontSize: '16px',
                                  color: 'white',
                                  fontWeight: 'bold',
                                  boxShadow: '0 3px 8px rgba(239, 68, 68, 0.3)',
                                }}
                              >
                                📺
                              </div>
                              <span
                                style={{
                                  fontSize: '20px',
                                  fontWeight: '700',
                                  color: '#1f2937',
                                  letterSpacing: '0.3px',
                                }}
                              >
                                YouTube URL
                              </span>
                              <span
                                style={{
                                  fontSize: '12px',
                                  color: '#9ca3af',
                                  fontWeight: '500',
                                  background: '#f3f4f6',
                                  padding: '2px 8px',
                                  borderRadius: '6px',
                                }}
                              >
                                Optional
                              </span>
                            </div>
                          }
                          validateTrigger={['onChange', 'onBlur']}
                          rules={[{ type: 'url', message: 'Please enter a valid URL' }]}
                          style={{ marginBottom: '8px' }}
                          className="custom-input"
                        >
                          <Input
                            placeholder="https://youtube.com/shorts/UmVHNQ7QDcU?si=9-AiZ-lnszuZJxx3"
                            size="large"
                            onChange={(e) => {
                              // 确保表单值被正确设置
                              form.setFieldsValue({
                                [`multimedia_url_${language}`]: e.target.value,
                              });
                            }}
                            style={{
                              fontSize: '16px',
                              borderRadius: '12px',
                              border: '2px solid #e5e7eb',
                              padding: '14px 18px',
                              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                              transition: 'all 0.2s ease',
                            }}
                            prefix={
                              <span
                                style={{
                                  fontSize: '16px',
                                  marginRight: '8px',
                                  color: '#ef4444',
                                }}
                              >
                                ▶️
                              </span>
                            }
                          />
                        </Form.Item>
                        <p
                          style={{
                            fontSize: '16px',
                            color: '#6b7280',
                            fontWeight: '500',
                            marginTop: '-4px',
                            fontStyle: 'italic',
                          }}
                        >
                          Recommended: YouTube video link (regular videos, Shorts, or live streams)
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </>
  );
};

export default ContentStep;
