import React, { useState, useEffect } from 'react';
import { Form, Select, Input, FormInstance } from 'antd';
import { LANGUAGE_OPTIONS } from '@/utils';

interface BasicInfoStepProps {
  form: FormInstance;
  stepData: any;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ form, stepData }) => {
  const [languages, setLanguages] = useState<string[]>(['en']);
  const [slugLength, setSlugLength] = useState(0);

  // 初始化表单值
  useEffect(() => {
    const initialValues: any = {};

    if (stepData.languages) {
      setLanguages(stepData.languages);
      initialValues.languages = stepData.languages;
    }

    if (stepData.slug) {
      initialValues.slug = stepData.slug;
      setSlugLength(stepData.slug.length);
    }

    if (Object.keys(initialValues).length > 0) {
      form.setFieldsValue(initialValues);
    }
  }, [stepData, form]);

  const handleLanguageChange = (selectedLanguages: string[]) => {
    setLanguages(selectedLanguages);
    form.setFieldsValue({ languages: selectedLanguages });
  };

  const handleSlugChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSlugLength(value.length);
    // 确保表单值被正确设置
    form.setFieldsValue({ slug: value });
  };

  return (
    <>
      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          /* 使用全局custom-input样式，这里不需要重复定义 */
        `}
      </style>

      {/* 整体表单容器 */}
      <div
        style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 100%)',
          border: '2px solid #e2e8f0',
          borderRadius: '24px',
          padding: '40px',
          boxShadow: '0 12px 32px rgba(0, 0, 0, 0.08)',
          position: 'relative',
          overflow: 'hidden',
          width: '96%',
          margin: '0 auto',
          height: '100%',
        }}
      >
        {/* 装饰性背景元素 */}
        <div
          style={{
            position: 'absolute',
            top: '-40px',
            right: '-40px',
            width: '120px',
            height: '120px',
            background: 'linear-gradient(135deg, #10b981, #059669)',
            borderRadius: '50%',
            opacity: '0.04',
          }}
        />
        <div
          style={{
            position: 'absolute',
            bottom: '-50px',
            left: '-50px',
            width: '150px',
            height: '150px',
            background: 'linear-gradient(135deg, #f59e0b, #d97706)',
            borderRadius: '50%',
            opacity: '0.03',
          }}
        />

        {/* 表单标题 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '16px',
            marginBottom: '20px',
            paddingBottom: '20px',
            borderBottom: '3px solid #e2e8f0',
            position: 'relative',
            zIndex: 1,
          }}
        >
          <div
            style={{
              width: '56px',
              height: '56px',
              background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
              borderRadius: '18px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '24px',
              boxShadow: '0 6px 16px rgba(16, 185, 129, 0.3)',
            }}
          >
            ⚙️
          </div>
          <div>
            <div
              style={{
                fontSize: '28px',
                fontWeight: '800',
                color: '#1f2937',
                letterSpacing: '0.4px',
              }}
            >
              Basic Information
            </div>
            <div
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
              }}
            >
              Configure languages and event identifier
            </div>
          </div>
        </div>

        {/* 表单内容 */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            gap: '32px',
            position: 'relative',
            zIndex: 1,
          }}
        >
          {/* 语言选择 */}
          <div>
            <Form.Item
              name="languages"
              label={
                <span
                  style={{
                    fontSize: '24px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  🌍 Languages
                </span>
              }
              rules={[{ required: true, message: 'Please select at least one language' }]}
              initialValue={['en']}
              style={{ marginBottom: '16px' }}
            >
              <Select
                mode="multiple"
                allowClear
                style={{
                  width: '100%',
                  fontSize: '18px',
                  borderRadius: '12px',
                }}
                placeholder="Choose languages for your event"
                value={languages}
                onChange={handleLanguageChange}
                options={LANGUAGE_OPTIONS}
                size="large"
                tagRender={(props) => {
                  const { label, closable, onClose } = props;
                  return (
                    <span
                      style={{
                        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                        color: 'white',
                        padding: '6px 14px',
                        borderRadius: '8px',
                        fontSize: '16px',
                        fontWeight: '600',
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '8px',
                        margin: '3px',
                        boxShadow: '0 3px 6px rgba(16, 185, 129, 0.3)',
                        transition: 'all 0.2s ease',
                      }}
                    >
                      {label}
                      {closable && (
                        <span
                          onClick={onClose}
                          style={{
                            cursor: 'pointer',
                            fontSize: '14px',
                            opacity: 0.8,
                            width: '20px',
                            height: '20px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            borderRadius: '50%',
                            transition: 'all 0.2s ease',
                            background: 'rgba(255, 255, 255, 0.2)',
                          }}
                          onMouseEnter={(e) => {
                            (e.target as HTMLElement).style.opacity = '1';
                            (e.target as HTMLElement).style.background = 'rgba(255, 255, 255, 0.3)';
                          }}
                          onMouseLeave={(e) => {
                            (e.target as HTMLElement).style.opacity = '0.8';
                            (e.target as HTMLElement).style.background = 'rgba(255, 255, 255, 0.2)';
                          }}
                        >
                          ×
                        </span>
                      )}
                    </span>
                  );
                }}
              />
            </Form.Item>

            <p
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
                fontStyle: 'italic',
                margin: '0px',
              }}
            >
              Select which languages your event will support
            </p>
          </div>

          {/* Event Slug */}
          <div>
            <Form.Item
              name="slug"
              label={
                <span
                  style={{
                    fontSize: '24px',
                    fontWeight: '700',
                    color: '#1f2937',
                    letterSpacing: '0.3px',
                  }}
                >
                  🔗 Event Slug
                </span>
              }
              validateTrigger={['onChange', 'onBlur']}
              rules={[
                {
                  required: true,
                  message: 'Please enter a slug for your event',
                  whitespace: true,
                },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: 'Only letters, numbers, and underscores are allowed',
                },
                {
                  min: 3,
                  message: 'Slug must be at least 3 characters long',
                },
              ]}
              style={{ marginBottom: '16px' }}
              className="custom-input"
            >
              <Input
                maxLength={30}
                onChange={handleSlugChange}
                suffix={
                  <span
                    style={{
                      color: slugLength > 25 ? '#ef4444' : '#6b7280',
                      fontWeight: '600',
                      fontSize: '14px',
                    }}
                  >
                    {slugLength}/30
                  </span>
                }
                placeholder="my_event_name"
                size="large"
                style={{
                  fontSize: '16px',
                  borderRadius: '12px',
                  border: '2px solid #e5e7eb',
                  padding: '12px 16px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
                  transition: 'all 0.2s ease',
                }}
              />
            </Form.Item>

            <p
              style={{
                fontSize: '16px',
                color: '#6b7280',
                fontWeight: '500',
                fontStyle: 'italic',
                margin: 0,
              }}
            >
              Unique identifier for your event URL (cannot be changed later)
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default BasicInfoStep;
