import React, { useState, useEffect } from 'react';
import axios from 'axios';
import dayjs from 'dayjs';
import { Modal, Form, Input, Row, Col, Divider, DatePicker, Button, message, Radio } from 'antd';
import MarkdownEditor from '@/components/MarkdownEditor';
import ImageUpload from '@/components/ImageUpload';
import {
  clearFormData,
  decodeFormData,
  formatRangePickerDates,
  parseRulesContent,
  encodeBase64,
  calculateImageUrl,
  processTitles,
  getCookie,
  stripMarkdown,
} from '@/utils';

interface DetailModalProps {
  visible: boolean;
  record: any;
  onCancel: () => void;
  onEdit: (eventParams: any, fullTitle?: string) => Promise<void>;
}

const currentYear = dayjs().year();
const minDate = dayjs(`${currentYear}-01-01`);
const maxDate = dayjs(`${currentYear + 3}-12-31`);

const DetailModal: React.FC<DetailModalProps> = ({ visible, record, onCancel, onEdit }) => {
  const [form] = Form.useForm();
  const [slugLength, setSlugLength] = useState(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [rulesValue, setRulesValue] = useState<string>('');

  // 检查用户角色
  const userRole = getCookie('jwt-role');
  const isEventWriter = userRole === 'event_writer';
  const isAdmin = userRole === 'admin';

  useEffect(() => {
    if (record) {
      const languages = record.title.map((item: { language: string }) => item.language);
      const slug = record.slug.find(
        (item: { language: string }) => item.language === 'en'
      )?.content;
      const slugWithoutTimestamp = slug ? slug.replace(/_\d+$/, '') : '';
      setSlugLength(slugWithoutTimestamp.length);

      const parsedRules = parseRulesContent(record.rules);
      setRulesValue(parsedRules);

      const fieldsValue = {
        ...record,
        date_range: [dayjs.utc(record.start_date), dayjs.utc(record.end_date)],
        rules: parsedRules,
        slug: slugWithoutTimestamp,
        image: record.image ? [{ url: record.image }] : '',
        icon: record.icon ? [{ url: record.icon }] : '',
        active: record.active,
        close: record.closed,
      };

      languages.forEach((language: string) => {
        fieldsValue[`slug_${language}`] =
          record.slug.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`title_${language}`] =
          record.title.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`description_${language}`] =
          record.description.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
        fieldsValue[`multimedia_url_${language}`] =
          record.multimedia_url.find(
            (item: { language: string; content: string }) => item.language === language
          )?.content || '';
      });
      form.setFieldsValue(fieldsValue);
    }
  }, [record]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const { start_date, end_date } = formatRangePickerDates(values.date_range);
      const fullTitle = processTitles(values);
      const { slug, title, description, multimedia_url } = decodeFormData(values);
      const clearValues = clearFormData(values);

      let formattedValues = {
        ...clearValues,
        start_date,
        end_date,
        slug,
        description,
        icon: clearValues.icon ? calculateImageUrl(clearValues, 'icon') : '',
        image: clearValues.image ? calculateImageUrl(clearValues, 'image') : '',
        multimedia_url: multimedia_url,
      };

      // admin 可以更新所有字段，event_writer 不能更新 title 和 rules
      // 从 formattedValues 中删除 id 字段（如果存在）
      const { id, ...valuesWithoutId } = formattedValues;

      if (isAdmin) {
        // 调试信息：打印原始 rules 内容
        console.log('=== Rules Debug Info ===');
        console.log('Original rules value:', JSON.stringify(values.rules));
        console.log('Original rules (with visible newlines):', values.rules.replace(/\n/g, '\\n'));
        console.log('Original rules length:', values.rules.length);

        // 清理 rules 中的 markdown 语法
        const cleanedRules = stripMarkdown(values.rules);

        // 调试信息：打印清理后的 rules 内容
        console.log('Cleaned rules:', JSON.stringify(cleanedRules));
        console.log('Cleaned rules (with visible newlines):', cleanedRules.replace(/\n/g, '\\n'));
        console.log('Cleaned rules length:', cleanedRules.length);

        const encodedRules = encodeBase64(cleanedRules);
        console.log('Encoded rules:', encodedRules);
        console.log('========================');

        formattedValues = {
          ...valuesWithoutId,
          title, // 用户编辑的 title
          rules: encodedRules, // 用户编辑的 rules（已清理 markdown）
        };
      } else {
        const { rules, ...valuesWithoutRules } = valuesWithoutId;
        formattedValues = {
          ...valuesWithoutRules,
        };
      }

      await onEdit(formattedValues, fullTitle);
      setLoading(false);
      onCancel();
    } catch (error) {
      setLoading(false);
      handleEditError(error);
      // 不要调用 onCancel()，让模态框保持打开状态以便用户修改后重试
    }
  };

  const handleEditError = (error: any) => {
    if (axios.isAxiosError(error)) {
      const errorData = error.response?.data;

      // 特殊处理 webhook 验证错误
      if (
        errorData?.error === 'received invalid response from input validation webhook' &&
        errorData?.code === 'unexpected'
      ) {
        message.error('Max allowed events number reached.');
        return; // 不关闭模态框，让用户可以修改后重试
      }

      // 处理权限错误
      if (errorData?.errors?.[0]?.extensions?.code === 'permission-error') {
        message.error(
          'Permission denied: You do not have the required permissions to edit this event.'
        );
        return;
      }

      // 处理约束违反错误
      if (errorData?.code === 'constraint-violation') {
        message.error(
          'Data constraint violation: Please check your input data for conflicts or invalid values.'
        );
        return;
      }

      // 处理网络错误
      if (error.code === 'NETWORK_ERROR' || !error.response) {
        message.error('Network error: Please check your connection and try again.');
        return;
      }

      // 处理其他 HTTP 错误
      const statusCode = error.response?.status;
      if (statusCode === 400) {
        message.error('Bad request: Please check your input data and try again.');
      } else if (statusCode === 401) {
        message.error('Unauthorized: Please log in again.');
      } else if (statusCode === 403) {
        message.error('Forbidden: You do not have permission to perform this action.');
      } else if (statusCode === 404) {
        message.error('Not found: The event you are trying to edit does not exist.');
      } else if (statusCode === 500) {
        message.error('Server error: Please try again later or contact support.');
      } else {
        // 通用错误处理
        const errorMessage = errorData?.error || errorData?.message || error.message;
        message.error(`Edit failed: ${errorMessage}`);
      }
    } else if (error instanceof Error) {
      // 处理 JavaScript 错误
      message.error(`Edit failed: ${error.message}`);
    } else {
      // 处理未知错误类型
      message.error('Failed to edit event: An unexpected error occurred. Please try again.');
    }
  };

  const disabledDate = (current: any) => {
    return current && (current < minDate || current > maxDate);
  };

  return (
    <Modal
      width={800}
      title={isEventWriter ? 'Event Details (Title and Rules are read-only)' : 'Event Details'}
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleOk}>
          Save
        </Button>,
      ]}
    >
      {record && (
        <Form form={form} layout="inline">
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="Slug"
                name="slug"
                rules={[
                  { required: true, message: 'Slug is required' },
                  {
                    pattern: /^[a-zA-Z0-9_]+$/,
                    message: 'Only letters, numbers, and underscores are allowed',
                  },
                ]}
              >
                <Input maxLength={30} disabled suffix={`${slugLength}/30`} />
              </Form.Item>

              <Divider dashed style={{ margin: '12px 0' }} />

              {record.title.map((item: { language: string; content: string }, index: number) => (
                <div
                  key={item.language + '_' + index}
                  style={{ display: 'flex', flexDirection: 'column', gap: 4 }}
                >
                  <Form.Item
                    label={`Title (${item.language})${isEventWriter ? ' (Read-only)' : ''}`}
                    name={`title_${item.language}`}
                  >
                    <Input
                      disabled={isEventWriter}
                      style={{
                        backgroundColor: isEventWriter ? '#f5f5f5' : undefined,
                        cursor: isEventWriter ? 'not-allowed' : undefined,
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    label={`Description (${item.language})`}
                    name={`description_${item.language}`}
                  >
                    <Input />
                  </Form.Item>
                  <Form.Item
                    name={`multimedia_url_${item.language}`}
                    label={`Media_url (${item.language})`}
                  >
                    <Input />
                  </Form.Item>
                  <Divider dashed style={{ margin: '8px 0' }} />
                </div>
              ))}
            </Col>
            <Col
              span={12}
              style={{
                borderLeft: '1px solid #f0f0f0',
                display: 'flex',
                flexDirection: 'column',
                gap: 6,
              }}
            >
              <Form.Item label="ID" name="id">
                <Input readOnly disabled />
              </Form.Item>

              <Form.Item
                label="Date:"
                name="date_range"
                layout="vertical"
                rules={[{ required: true, message: 'Please select a date range' }]}
              >
                <DatePicker.RangePicker
                  disabledDate={disabledDate}
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                />
              </Form.Item>

              <Divider dashed style={{ margin: '12px 0' }} />

              <Row>
                <Col span={12}>
                  <Form.Item
                    name="icon"
                    label="Icon"
                    valuePropName="fileList"
                    rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                    labelCol={{ span: 8 }}
                  >
                    <ImageUpload prefix="event" maxSizeKB={64} value={record.icon} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="image"
                    label="Image"
                    valuePropName="fileList"
                    rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                    labelCol={{ span: 10 }}
                  >
                    <ImageUpload prefix="event" maxSizeKB={64} value={record.image} />
                  </Form.Item>
                </Col>
              </Row>

              <Divider dashed style={{ margin: '12px 0' }} />

              <Row>
                <Col span={24}>
                  <Form.Item
                    label="Active"
                    name="active"
                    rules={[{ required: true, message: 'Please select active status' }]}
                  >
                    <Radio.Group>
                      <Radio value={true}>Yes</Radio>
                      <Radio value={false}>No</Radio>
                    </Radio.Group>
                  </Form.Item>

                  <Form.Item
                    label="Closed"
                    name="closed"
                    rules={[{ required: true, message: 'Please select closed status' }]}
                  >
                    <Radio.Group>
                      <Radio value={true}>Yes</Radio>
                      <Radio value={false}>No</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>
              </Row>
            </Col>
          </Row>

          <Divider dashed style={{ margin: '12px 0' }} />

          <Row style={{ width: '98%' }}>
            <Col span={24}>
              <Form.Item label={`Rules${!isAdmin ? ' (Read-only)' : ''}`} name="rules">
                <div
                  style={{
                    opacity: !isAdmin ? 0.8 : 1,
                    backgroundColor: !isAdmin ? '#f9f9f9' : 'transparent',
                    padding: !isAdmin ? '8px' : '0',
                    borderRadius: !isAdmin ? '6px' : '0',
                    border: !isAdmin ? '1px solid #d9d9d9' : 'none',
                    // 移除 pointerEvents: 'none' 以允许滚动
                  }}
                >
                  <MarkdownEditor
                    value={rulesValue}
                    onChange={(newValue) => {
                      if (isAdmin) {
                        console.log('=== MarkdownEditor onChange ===');
                        console.log('New value:', JSON.stringify(newValue));
                        console.log(
                          'New value (with visible newlines):',
                          newValue.replace(/\n/g, '\\n')
                        );
                        console.log('New value length:', newValue.length);
                        console.log('===============================');
                        setRulesValue(newValue);
                        form.setFieldsValue({ rules: newValue });
                      }
                    }}
                    readOnly={!isAdmin}
                    disableMarkdown={true}
                    needToolBar={false}
                  />
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      )}
    </Modal>
  );
};

export default DetailModal;
