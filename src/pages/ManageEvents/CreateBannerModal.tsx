import React, { useState, useEffect } from 'react';
import { Modal, Checkbox, App, Card, Typography, Space, Tag, Spin } from 'antd';
import { GlobalOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { fetchAddBanner, fetchBannerAllLanguagesSeparated } from '@/api/banner';

const { Title, Text } = Typography;

interface CreateBannerModalProps {
  visible: boolean;
  onCancel: () => void;
  selectedEvent: any;
}

const CreateBannerModal: React.FC<CreateBannerModalProps> = ({
  visible,
  onCancel,
  selectedEvent,
}) => {
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [existingRegions, setExistingRegions] = useState<string[]>([]);
  const [checkingExisting, setCheckingExisting] = useState(false);
  const { message } = App.useApp();

  // 检查现有Banner
  useEffect(() => {
    const checkExistingBanners = async () => {
      if (!visible || !selectedEvent) {
        setExistingRegions([]);
        return;
      }

      setCheckingExisting(true);
      try {
        // 获取所有语言的Banner数据
        const result = await fetchBannerAllLanguagesSeparated();
        const allBanners = result.data || { en: [], zh: [], ko: [] };

        // 检查当前事件ID在哪些语言中存在
        const existingLanguages: string[] = [];

        Object.entries(allBanners).forEach(([language, banners]) => {
          const hasEventBanner = banners.some(
            (banner: any) => banner.event_id === selectedEvent.id
          );
          if (hasEventBanner) {
            existingLanguages.push(language);
          }
        });

        setExistingRegions(existingLanguages);

        // 将已存在的地区设置为选中状态
        setSelectedRegions(existingLanguages);
      } catch (error) {
        console.error('Error checking existing banners:', error);
        setExistingRegions([]);
      } finally {
        setCheckingExisting(false);
      }
    };

    checkExistingBanners();
  }, [visible, selectedEvent]);

  const handleOk = async () => {
    if (!selectedEvent || selectedRegions.length === 0) {
      message.warning('Please select at least one region');
      return;
    }

    // 过滤出需要创建的地区（排除已存在的）
    const regionsToCreate = selectedRegions.filter((region) => !existingRegions.includes(region));

    if (regionsToCreate.length === 0) {
      message.info('All selected regions already have banners for this event');
      return;
    }

    setLoading(true);
    try {
      // 安全地提取title
      let title = '';

      if (Array.isArray(selectedEvent.title) && selectedEvent.title.length > 0) {
        title =
          typeof selectedEvent.title[0] === 'object'
            ? selectedEvent.title[0].content
            : String(selectedEvent.title[0]);
      } else if (typeof selectedEvent.title === 'object' && selectedEvent.title !== null) {
        title = selectedEvent.title.content || JSON.stringify(selectedEvent.title);
      } else {
        title = String(selectedEvent.title || '');
      }

      // 只为新地区创建Banner
      const bannerPromises = regionsToCreate.map((region) => {
        const bannerParams = {
          event_id: selectedEvent.id,
          title: title,
          image_url: selectedEvent.image || '',
          region: region,
          feature: false,
          timestamp: dayjs.utc().toISOString(),
        };
        return fetchAddBanner(bannerParams);
      });

      await Promise.all(bannerPromises);

      const existingCount = existingRegions.filter((region) =>
        selectedRegions.includes(region)
      ).length;
      const createdCount = regionsToCreate.length;

      if (existingCount > 0 && createdCount > 0) {
        message.success(
          `Created ${createdCount} new banner(s). ${existingCount} region(s) already had banners.`
        );
      } else if (createdCount > 0) {
        message.success(`Banner created successfully for ${createdCount} region(s)`);
      }

      // 重置状态并关闭Modal
      setSelectedRegions([]);
      onCancel();
    } catch (error) {
      message.error('Failed to create banner');
      console.error('Error creating banner:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedRegions([]);
    onCancel();
  };

  // 安全地提取显示信息
  const getDisplayInfo = () => {
    if (!selectedEvent) return { title: 'N/A' };

    let title = '';

    if (Array.isArray(selectedEvent.title) && selectedEvent.title.length > 0) {
      title =
        typeof selectedEvent.title[0] === 'object'
          ? selectedEvent.title[0].content
          : String(selectedEvent.title[0]);
    } else if (typeof selectedEvent.title === 'object' && selectedEvent.title !== null) {
      title = selectedEvent.title.content || 'N/A';
    } else {
      title = String(selectedEvent.title || 'N/A');
    }

    return { title };
  };

  const displayInfo = getDisplayInfo();

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0, color: '#262626' }}>
            Create Multi-Language Banner
          </Title>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      okButtonProps={{ disabled: checkingExisting }}
      okText="Create Banner"
      cancelText="Cancel"
      width={600}
      centered
    >
      <div style={{ padding: '12px 0' }}>
        {/* 地区选择区域 */}
        <Card
          size="small"
          style={{
            marginBottom: '20px',
            borderRadius: '8px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
          }}
        >
          <div style={{ marginBottom: '16px' }}>
            <Title
              level={5}
              style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}
            >
              <GlobalOutlined style={{ color: '#1890ff' }} />
              Select Target Regions
              {checkingExisting && <Spin size="small" style={{ marginLeft: '8px' }} />}
            </Title>
            {checkingExisting && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Checking existing banners...
              </Text>
            )}
          </div>

          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <div
              style={{
                padding: '12px 16px',
                border: '1px solid #f0f0f0',
                borderRadius: '6px',
                backgroundColor: existingRegions.includes('en') ? '#f5f5f5' : '#fafafa',
                transition: 'all 0.3s ease',
              }}
            >
              <Checkbox
                checked={selectedRegions.includes('en')}
                disabled={checkingExisting || existingRegions.includes('en')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedRegions([...selectedRegions, 'en']);
                  } else {
                    setSelectedRegions(selectedRegions.filter((region) => region !== 'en'));
                  }
                }}
                style={{ fontSize: '14px', fontWeight: 500 }}
              >
                <Space align="center">
                  <span>🇺🇸</span>
                  <span>English (EN)</span>
                  {existingRegions.includes('en') && (
                    <Tag color="orange" style={{ fontSize: '12px' }}>
                      <CheckCircleOutlined style={{ marginRight: '4px' }} />
                      Already exists
                    </Tag>
                  )}
                </Space>
              </Checkbox>
            </div>
            <div
              style={{
                padding: '12px 16px',
                border: '1px solid #f0f0f0',
                borderRadius: '6px',
                backgroundColor: existingRegions.includes('zh') ? '#f5f5f5' : '#fafafa',
                transition: 'all 0.3s ease',
              }}
            >
              <Checkbox
                checked={selectedRegions.includes('zh')}
                disabled={checkingExisting || existingRegions.includes('zh')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedRegions([...selectedRegions, 'zh']);
                  } else {
                    setSelectedRegions(selectedRegions.filter((region) => region !== 'zh'));
                  }
                }}
                style={{ fontSize: '14px', fontWeight: 500 }}
              >
                <Space align="center">
                  <span>🇨🇳</span>
                  <span>中文 (ZH)</span>
                  {existingRegions.includes('zh') && (
                    <Tag color="orange" style={{ fontSize: '12px' }}>
                      <CheckCircleOutlined style={{ marginRight: '4px' }} />
                      Already exists
                    </Tag>
                  )}
                </Space>
              </Checkbox>
            </div>
            <div
              style={{
                padding: '12px 16px',
                border: '1px solid #f0f0f0',
                borderRadius: '6px',
                backgroundColor: existingRegions.includes('ko') ? '#f5f5f5' : '#fafafa',
                transition: 'all 0.3s ease',
              }}
            >
              <Checkbox
                checked={selectedRegions.includes('ko')}
                disabled={checkingExisting || existingRegions.includes('ko')}
                onChange={(e) => {
                  if (e.target.checked) {
                    setSelectedRegions([...selectedRegions, 'ko']);
                  } else {
                    setSelectedRegions(selectedRegions.filter((region) => region !== 'ko'));
                  }
                }}
                style={{ fontSize: '14px', fontWeight: 500 }}
              >
                <Space align="center">
                  <span>🇰🇷</span>
                  <span>한국어 (KO)</span>
                  {existingRegions.includes('ko') && (
                    <Tag color="orange" style={{ fontSize: '12px' }}>
                      <CheckCircleOutlined style={{ marginRight: '4px' }} />
                      Already exists
                    </Tag>
                  )}
                </Space>
              </Checkbox>
            </div>
          </Space>
        </Card>

        {/* Event信息展示区域 */}
        {selectedEvent && (
          <Card
            size="small"
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              border: '1px solid #e8f4fd',
            }}
          >
            <Title
              level={5}
              style={{ margin: '0 0 16px 0', display: 'flex', alignItems: 'center', gap: '8px' }}
            >
              <ExclamationCircleOutlined style={{ color: '#1890ff' }} />
              Selected Event Information
            </Title>

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              <div
                style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
              >
                <Text strong style={{ color: '#595959' }}>
                  Event ID:
                </Text>
                <Tag color="blue">{selectedEvent.id}</Tag>
              </div>

              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'flex-start',
                }}
              >
                <Text strong style={{ color: '#595959', minWidth: '80px' }}>
                  Title:
                </Text>
                <Text
                  style={{
                    maxWidth: '300px',
                    textAlign: 'right',
                    wordBreak: 'break-word',
                  }}
                >
                  {displayInfo.title}
                </Text>
              </div>
            </Space>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default CreateBannerModal;
