import React, { useState, useEffect, useCallback } from 'react';
import {
  Table,
  Button,
  Space,
  Image,
  App,
  Row,
  Col,
  Card,
  Typography,
  Tooltip,
  Select,
  Dropdown,
  Tag,
} from 'antd';
import type { MenuProps } from 'antd';
import axios from 'axios';
import type { ColumnsType } from 'antd/es/table';
import {
  fetchEventsListByTag,
  fetchAddEvent,
  fetchAddEventTags,
  fetchUpdateEvent,
} from '@/api/events';
import { EventDataType } from '@/types/events';
import { formatDate, decodeEventData, getLocalStorage } from '@/utils';
import { PoweroffOutlined, MoreOutlined } from '@ant-design/icons';
import Breadcrumb from '@/components/Breadcrumb';
import AddModal from './AddModal';
import DetailModal from './DetailModal';
import WarningModal from './WarningModal';
import { useNavigate } from 'react-router-dom';

// 状态颜色映射
const statusColorMap = {
  yes: 'success' as const,
  no: 'error' as const,
};

const ManageEvents: React.FC = () => {
  const userWallet = getLocalStorage('userWallet');
  const { message } = App.useApp();
  const navigate = useNavigate();

  const [data, setData] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [eventId, setEventId] = useState<number | undefined>(undefined);
  const [isWarningModalVisible, setIsWarningModalVisible] = useState(false);
  const [warningModalType, setWarningModalType] = useState<'delete' | 'publish'>('delete');
  const [filteredData, setFilteredData] = useState<any[]>([]);

  const [currentRecord, setCurrentRecord] = useState<EventDataType | null>(null);
  const [loading, setLoading] = useState<{ [key: number]: boolean }>({});
  // const sportTags = [61, 63]; // dev
  const sportTags = [51, 59]; // ol

  const fetchData = useCallback(async () => {
    try {
      const result = await fetchEventsListByTag(userWallet, sportTags);
      const initResult = decodeEventData(result.data.events).sort((a, b) => b.id - a.id);
      setData(initResult);
      setFilteredData(initResult);
    } catch (error) {
      console.error('Error fetching events list by tag:', error);
      message.error('Failed to fetch events list');
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, []);

  const handleAddEvent = async (eventParams: any, selectedTagIdList: any) => {
    const eventCb = await fetchAddEvent(eventParams);

    // 去重标签ID并转换为数字类型
    const uniqueTagIds = [...new Set(selectedTagIdList.map((id: any) => Number(id)))];

    // 串行添加标签，避免并发请求导致的重复
    for (const tag_id of uniqueTagIds) {
      try {
        const tagParams = { event_id: eventCb.id, tag_id: tag_id as number };
        await fetchAddEventTags(tagParams);
      } catch (error) {
        if (
          axios.isAxiosError(error) &&
          error.response?.data?.code === 'constraint-violation' &&
          error.response?.data?.error?.includes('event_tags_event_id_tag_id_key')
        ) {
          console.warn(`Tag ${tag_id} already exists for event ${eventCb.id}, skipping...`);
          continue;
        }
        // 其他错误则抛出
        throw error;
      }
    }
  };

  const handleEditEvent = async (eventParams: any) => {
    try {
      // 优先使用 eventParams 中的 id，如果没有则使用 currentRecord.id
      const eventId = eventParams.id || currentRecord?.id;
      if (eventId !== undefined) {
        // 从 eventParams 中移除 id，避免传递给后端
        const { id, ...updateParams } = eventParams;
        await fetchUpdateEvent(eventId, updateParams);
        message.success('Event edited successfully');
      } else {
        message.error('Event ID is undefined');
      }
      fetchData();
    } catch (error) {
      // 检查是否是 webhook 验证错误
      if (axios.isAxiosError(error)) {
        const errorData = error.response?.data;

        // 特殊处理 webhook 验证错误
        if (
          errorData?.error === 'received invalid response from input validation webhook' &&
          errorData?.code === 'unexpected'
        ) {
          message.error('Max allowed events number reached.');
          throw error; // 重新抛出错误以阻塞编辑流程
        }

        // 处理权限错误
        if (errorData?.errors?.[0]?.extensions?.code === 'permission-error') {
          message.error(
            'Permission denied: You do not have the required permissions to edit this event.'
          );
          throw error;
        }

        // 处理约束违反错误
        if (errorData?.code === 'constraint-violation') {
          message.error(
            'Data constraint violation: Please check your input data for conflicts or invalid values.'
          );
          throw error;
        }

        // 处理网络错误
        if (error.code === 'NETWORK_ERROR' || !error.response) {
          message.error('Network error: Please check your connection and try again.');
          throw error;
        }

        // 处理其他 HTTP 错误
        const statusCode = error.response?.status;
        if (statusCode === 400) {
          message.error('Bad request: Please check your input data and try again.');
        } else if (statusCode === 401) {
          message.error('Unauthorized: Please log in again.');
        } else if (statusCode === 403) {
          message.error('Forbidden: You do not have permission to perform this action.');
        } else if (statusCode === 404) {
          message.error('Not found: The event you are trying to edit does not exist.');
        } else if (statusCode === 500) {
          message.error('Server error: Please try again later or contact support.');
        } else {
          // 通用错误处理
          const errorMessage = errorData?.error || errorData?.message || error.message;
          message.error(`Edit failed: ${errorMessage}`);
        }
        throw error; // 重新抛出错误以阻塞编辑流程
      } else if (error instanceof Error) {
        message.error(`Edit failed: ${error.message}`);
        throw error;
      } else {
        message.error('Failed to edit event: An unexpected error occurred. Please try again.');
        throw error; // 重新抛出错误以阻塞编辑流程
      }
    }
  };

  const handleFilterChange = (value: string | undefined) => {
    if (value === 'active') {
      setFilteredData(data.filter((item) => !item.closed));
    } else if (value === 'closed') {
      setFilteredData(data.filter((item) => item.closed));
    } else {
      // value === 'all' 时显示所有数据
      setFilteredData(data);
    }
  };

  const columns: ColumnsType<EventDataType> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      fixed: 'left',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: 250,
      fixed: 'left',
      render: (text) => {
        const content = Array.isArray(text) && text.length > 0 ? text[0].content : text;
        return (
          <Tooltip title={content}>
            <Typography.Text strong>
              <div
                style={{
                  maxWidth: 230,
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {content}
              </div>
            </Typography.Text>
          </Tooltip>
        );
      },
    },
    {
      title: 'Icon',
      dataIndex: 'icon',
      key: 'icon',
      width: 80,
      fixed: 'left',
      render: (icon) => (
        <Image
          src={icon}
          alt="icon"
          width={40}
          height={40}
          style={{ objectFit: 'cover', objectPosition: 'center' }}
        />
      ),
    },
    {
      title: 'Active',
      dataIndex: 'active',
      key: 'active',
      width: 90,
      render: (active) => {
        const status = active ? 'yes' : 'no';
        const text = active ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    {
      title: 'Closed',
      dataIndex: 'closed',
      key: 'closed',
      width: 90,
      render: (closed) => {
        const status = closed ? 'yes' : 'no';
        const text = closed ? 'Yes' : 'No';
        return (
          <Tag color={statusColorMap[status]} className="capitalize">
            {text}
          </Tag>
        );
      },
    },
    {
      title: 'Updated Date(UTC)',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.updated_at ? new Date(a.updated_at).getTime() : 0;
        const dateB = b.updated_at ? new Date(b.updated_at).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Start Date(UTC)',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.start_date ? new Date(a.start_date).getTime() : 0;
        const dateB = b.start_date ? new Date(b.start_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'End Date(UTC)',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.end_date ? new Date(a.end_date).getTime() : 0;
        const dateB = b.end_date ? new Date(b.end_date).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record) => {
        const isPublished = record.neg_risk_market_id.length >= 5;

        // More 下拉菜单项
        const moreMenuItems: MenuProps['items'] = [
          ...(!isPublished
            ? [
                {
                  key: 'delete',
                  label: 'Delete',
                  danger: true,
                  onClick: () => {
                    setEventId(record.id);
                    setWarningModalType('delete');
                    setIsWarningModalVisible(true);
                  },
                },
              ]
            : []),
        ];

        return (
          <div style={{ display: 'flex', gap: '4px', alignItems: 'center', whiteSpace: 'nowrap' }}>
            <Button
              onClick={() => {
                setCurrentRecord(record);
                setIsDetailModalVisible(true);
              }}
            >
              Edit
            </Button>

            {!isPublished ? (
              <Button
                type="primary"
                key={record.id}
                loading={loading[record.id] || false}
                icon={<PoweroffOutlined />}
                onClick={() => {
                  setCurrentRecord(record);
                  setWarningModalType('publish');
                  setIsWarningModalVisible(true);
                }}
              >
                Publish
              </Button>
            ) : (
              <Button
                type="primary"
                key={record.id}
                onClick={() => navigate(`/questions?eventId=${record.id}`)}
              >
                Question
              </Button>
            )}

            {moreMenuItems.length > 0 && (
              <Dropdown menu={{ items: moreMenuItems }} trigger={['click']} placement="bottomRight">
                <Button icon={<MoreOutlined />} />
              </Dropdown>
            )}
          </div>
        );
      },
    },
  ];

  return (
    <div style={{ overflowX: 'hidden' }}>
      <Breadcrumb url="/#/events" text={'Event'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card title="Events Table">
            <Space style={{ marginBottom: '24px' }}>
              <Button type="primary" onClick={() => setIsModalVisible(true)}>
                Add Event
              </Button>
              <Select defaultValue="all" style={{ width: 200 }} onChange={handleFilterChange}>
                <Select.Option value="all">All</Select.Option>
                <Select.Option value="active">Active</Select.Option>
                <Select.Option value="closed">Closed</Select.Option>
              </Select>
            </Space>
            <div className="table-responsive">
              <Table
                columns={columns}
                dataSource={filteredData}
                pagination={false}
                className="ant-border-space"
                scroll={{ x: 1200 }}
              />
            </div>
          </Card>
        </Col>
      </Row>

      <AddModal
        visible={isModalVisible}
        setVisible={setIsModalVisible}
        onSubmit={handleAddEvent}
        fetchData={fetchData}
        onCancel={() => setIsModalVisible(false)}
      />
      <DetailModal
        visible={isDetailModalVisible}
        record={currentRecord}
        onCancel={() => setIsDetailModalVisible(false)}
        onEdit={handleEditEvent}
      />
      <WarningModal
        visible={isWarningModalVisible}
        onClose={() => setIsWarningModalVisible(false)}
        eventId={eventId}
        type={warningModalType}
        fetchData={fetchData}
        currentRecord={currentRecord}
        setLoading={setLoading}
      />
    </div>
  );
};

export default ManageEvents;
