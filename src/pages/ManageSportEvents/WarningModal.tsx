import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, message, Form, Input } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { fetchDeleteEvent } from '@/api/events';
import handleReleaseEvent from '@/utils/signature/createEventSignature';
import { formatDate } from '@/utils';

interface WarningModalProps {
  visible: boolean;
  onClose: () => void;
  eventId?: number;
  type: 'delete' | 'publish';
  fetchData?: () => void;
  currentRecord?: any;
  setLoading?: (loading: { [key: number]: boolean }) => void;
}

const WarningModal: React.FC<WarningModalProps> = ({
  visible,
  onClose,
  eventId,
  type,
  fetchData,
  currentRecord,
  setLoading,
}) => {
  const [form] = Form.useForm();
  const [confirmation, setConfirmation] = useState<string>('');
  const [delLoading, setDelLoading] = useState<boolean>(false);

  const handleConfirm = async () => {
    if (type === 'delete' && eventId) {
      try {
        setDelLoading(true);
        await fetchDeleteEvent(eventId);
        message.success('Event deleted successfully');
        if (fetchData) {
          fetchData();
        }
      } catch (error) {
        message.error('Failed to delete event');
      } finally {
        setDelLoading(false);
        onClose();
      }
    } else if (type === 'publish') {
      try {
        const values = await form.validateFields();
        if (values.confirmation.toUpperCase() !== 'YES') {
          message.error('Confirmation does not match');
          return;
        }
        if (currentRecord && setLoading) {
          onClose();
          form.resetFields();
          await handleReleaseEvent(currentRecord, setLoading, message, fetchData);
        }
        message.success('Event published successfully');
        if (fetchData) fetchData();
      } catch (error) {
        message.error(`Error: ${error}`);
      }
    }
  };

  const renderContent = () => {
    if (type === 'delete') {
      return (
        <>
          <ExclamationCircleOutlined style={{ fontSize: '48px', color: '#faad14' }} />
          <div style={{ fontSize: '18px', color: '#faad14', marginTop: '8px' }}>
            Are you sure you want to delete this event?
          </div>
        </>
      );
    } else if (type === 'publish') {
      return (
        <Form form={form} layout="vertical">
          <div style={{ textAlign: 'center', marginBottom: '16px' }}>
            <div style={{ fontSize: '18px', color: '#52c41a', marginTop: '8px' }}>
              Please confirm the event time in UMT format.
            </div>
            <div style={{ fontSize: '16px', color: '#000', marginTop: '8px' }}>
              <strong>Date: </strong>
              {currentRecord
                ? `${formatDate(currentRecord.start_date)} - ${formatDate(currentRecord.end_date)}`
                : 'N/A'}
            </div>
          </div>
          <Divider dashed style={{ margin: '12px 0' }} />
          <Form.Item
            name="confirmation"
            label="Confirmation"
            rules={[{ required: true, message: 'Please input YES to confirm' }]}
          >
            <Input
              value={confirmation}
              onChange={(e) => setConfirmation(e.target.value)}
              placeholder="Please input YES"
              style={{ textAlign: 'center' }}
            />
          </Form.Item>
        </Form>
      );
    }
  };

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={
        <>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            type="primary"
            danger={type === 'delete'}
            loading={delLoading}
            onClick={handleConfirm}
          >
            Confirm
          </Button>
        </>
      }
      centered
    >
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          padding: '12px',
          paddingBottom: 0,
        }}
      >
        {renderContent()}
      </div>
    </Modal>
  );
};

export default WarningModal;
