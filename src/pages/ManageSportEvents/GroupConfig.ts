export const nbaTeams = [
  {
    fullNameEN: 'Atlanta Hawks',
    fullNameZH: '亚特兰大老鹰',
    shortName: 'ATL',
    icon: '/logos/NBA/ATL.png',
    descriptionEN: 'Atlanta Hawks',
    descriptionZH: '亚特兰大老鹰',
  },
  {
    fullNameEN: 'Boston Celtics',
    fullNameZH: '波士顿凯尔特人',
    shortName: 'BOS',
    icon: '/logos/NBA/BOS.png',
    descriptionEN: 'Boston Celtics',
    descriptionZH: '波士顿凯尔特人',
  },
  {
    fullNameEN: 'Brooklyn Nets',
    fullNameZH: '布鲁克林篮网',
    shortName: 'BKN',
    icon: '/logos/NBA/BKN.png',
    descriptionEN: 'Brooklyn Nets',
    descriptionZH: '布鲁克林篮网',
  },
  {
    fullNameEN: 'Charlotte Hornets',
    fullNameZH: '夏洛特黄蜂',
    shortName: 'CHA',
    icon: '/logos/NBA/CHA.png',
    descriptionEN: 'Charlotte Hornets',
    descriptionZH: '夏洛特黄蜂',
  },
  {
    fullNameEN: 'Chicago Bulls',
    fullNameZH: '芝加哥公牛',
    shortName: 'CHI',
    icon: '/logos/NBA/CHI.png',
    descriptionEN: 'Chicago Bulls',
    descriptionZH: '芝加哥公牛',
  },
  {
    fullNameEN: 'Cleveland Cavaliers',
    fullNameZH: '克里夫兰骑士',
    shortName: 'CLE',
    icon: '/logos/NBA/CLE.png',
    descriptionEN: 'Cleveland Cavaliers',
    descriptionZH: '克里夫兰骑士',
  },
  {
    fullNameEN: 'Dallas Mavericks',
    fullNameZH: '达拉斯独行侠',
    shortName: 'DAL',
    icon: '/logos/NBA/DAL.png',
    descriptionEN: 'Dallas Mavericks',
    descriptionZH: '达拉斯独行侠',
  },
  {
    fullNameEN: 'Denver Nuggets',
    fullNameZH: '丹佛掘金',
    shortName: 'DEN',
    icon: '/logos/NBA/DEN.png',
    descriptionEN: 'Denver Nuggets',
    descriptionZH: '丹佛掘金',
  },
  {
    fullNameEN: 'Detroit Pistons',
    fullNameZH: '底特律活塞',
    shortName: 'DET',
    icon: '/logos/NBA/DET.png',
    descriptionEN: 'Detroit Pistons',
    descriptionZH: '底特律活塞',
  },
  {
    fullNameEN: 'Golden State Warriors',
    fullNameZH: '金州勇士',
    shortName: 'GSW',
    icon: '/logos/NBA/GSW.png',
    descriptionEN: 'Golden State Warriors',
    descriptionZH: '金州勇士',
  },
  {
    fullNameEN: 'Houston Rockets',
    fullNameZH: '休斯顿火箭',
    shortName: 'HOU',
    icon: '/logos/NBA/HOU.png',
    descriptionEN: 'Houston Rockets',
    descriptionZH: '休斯顿火箭',
  },
  {
    fullNameEN: 'Indiana Pacers',
    fullNameZH: '印第安纳步行者',
    shortName: 'IND',
    icon: '/logos/NBA/IND.png',
    descriptionEN: 'Indiana Pacers',
    descriptionZH: '印第安纳步行者',
  },
  {
    fullNameEN: 'Los Angeles Clippers',
    fullNameZH: '洛杉矶快船',
    shortName: 'LAC',
    icon: '/logos/NBA/LAC.png',
    descriptionEN: 'Los Angeles Clippers',
    descriptionZH: '洛杉矶快船',
  },
  {
    fullNameEN: 'Los Angeles Lakers',
    fullNameZH: '洛杉矶湖人',
    shortName: 'LAL',
    icon: '/logos/NBA/LAL.png',
    descriptionEN: 'Los Angeles Lakers',
    descriptionZH: '洛杉矶湖人',
  },
  {
    fullNameEN: 'Memphis Grizzlies',
    fullNameZH: '孟菲斯灰熊',
    shortName: 'MEM',
    icon: '/logos/NBA/MEM.png',
    descriptionEN: 'Memphis Grizzlies',
    descriptionZH: '孟菲斯灰熊',
  },
  {
    fullNameEN: 'Miami Heat',
    fullNameZH: '迈阿密热火',
    shortName: 'MIA',
    icon: '/logos/NBA/MIA.png',
    descriptionEN: 'Miami Heat',
    descriptionZH: '迈阿密热火',
  },
  {
    fullNameEN: 'Milwaukee Bucks',
    fullNameZH: '密尔沃基雄鹿',
    shortName: 'MIL',
    icon: '/logos/NBA/MIL.png',
    descriptionEN: 'Milwaukee Bucks',
    descriptionZH: '密尔沃基雄鹿',
  },
  {
    fullNameEN: 'Minnesota Timberwolves',
    fullNameZH: '明尼苏达森林狼',
    shortName: 'MIN',
    icon: '/logos/NBA/MIN.png',
    descriptionEN: 'Minnesota Timberwolves',
    descriptionZH: '明尼苏达森林狼',
  },
  {
    fullNameEN: 'New Orleans Pelicans',
    fullNameZH: '新奥尔良鹈鹕',
    shortName: 'NOP',
    icon: '/logos/NBA/NOP.png',
    descriptionEN: 'New Orleans Pelicans',
    descriptionZH: '新奥尔良鹈鹕',
  },
  {
    fullNameEN: 'New York Knicks',
    fullNameZH: '纽约尼克斯',
    shortName: 'NYK',
    icon: '/logos/NBA/NYK.png',
    descriptionEN: 'New York Knicks',
    descriptionZH: '纽约尼克斯',
  },
  {
    fullNameEN: 'Oklahoma City Thunder',
    fullNameZH: '俄克拉荷马城雷霆',
    shortName: 'OKC',
    icon: '/logos/NBA/OKC.png',
    descriptionEN: 'Oklahoma City Thunder',
    descriptionZH: '俄克拉荷马城雷霆',
  },
  {
    fullNameEN: 'Orlando Magic',
    fullNameZH: '奥兰多魔术',
    shortName: 'ORL',
    icon: '/logos/NBA/ORL.png',
    descriptionEN: 'Orlando Magic',
    descriptionZH: '奥兰多魔术',
  },
  {
    fullNameEN: 'Philadelphia 76ers',
    fullNameZH: '费城76人',
    shortName: 'PHI',
    icon: '/logos/NBA/PHI.png',
    descriptionEN: 'Philadelphia 76ers',
    descriptionZH: '费城76人',
  },
  {
    fullNameEN: 'Phoenix Suns',
    fullNameZH: '菲尼克斯太阳',
    shortName: 'PHX',
    icon: '/logos/NBA/PHX.png',
    descriptionEN: 'Phoenix Suns',
    descriptionZH: '菲尼克斯太阳',
  },
  {
    fullNameEN: 'Portland Trail Blazers',
    fullNameZH: '波特兰开拓者',
    shortName: 'POR',
    icon: '/logos/NBA/POR.png',
    descriptionEN: 'Portland Trail Blazers',
    descriptionZH: '波特兰开拓者',
  },
  {
    fullNameEN: 'Sacramento Kings',
    fullNameZH: '萨克拉门托国王',
    shortName: 'SAC',
    icon: '/logos/NBA/SAC.png',
    descriptionEN: 'Sacramento Kings',
    descriptionZH: '萨克拉门托国王',
  },
  {
    fullNameEN: 'San Antonio Spurs',
    fullNameZH: '圣安东尼奥马刺',
    shortName: 'SAS',
    icon: '/logos/NBA/SAS.png',
    descriptionEN: 'San Antonio Spurs',
    descriptionZH: '圣安东尼奥马刺',
  },
  {
    fullNameEN: 'Toronto Raptors',
    fullNameZH: '多伦多猛龙',
    shortName: 'TOR',
    icon: '/logos/NBA/TOR.png',
    descriptionEN: 'Toronto Raptors',
    descriptionZH: '多伦多猛龙',
  },
  {
    fullNameEN: 'Utah Jazz',
    fullNameZH: '犹他爵士',
    shortName: 'UTA',
    icon: '/logos/NBA/UTA.png',
    descriptionEN: 'Utah Jazz',
    descriptionZH: '犹他爵士',
  },
  {
    fullNameEN: 'Washington Wizards',
    fullNameZH: '华盛顿奇才',
    shortName: 'WAS',
    icon: '/logos/NBA/WAS.png',
    descriptionEN: 'Washington Wizards',
    descriptionZH: '华盛顿奇才',
  },
];
