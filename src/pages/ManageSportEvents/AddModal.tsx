import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Modal, Form, Input, message, DatePicker, Row, Col, Select, Divider } from 'antd';
import { EventDataType } from '@/types/events';
import { fetchTagsListByRegion } from '@/api/tags';
import ImageUpload from '@/components/ImageUpload';
import MarkdownEditor from '@/components/MarkdownEditor';
import {
  LANGUAGE_OPTIONS,
  formatRangePickerDates,
  getLocalStorage,
  calculateImageUrl,
  encodeBase64,
  stripMarkdown,
} from '@/utils';
const { RangePicker } = DatePicker;
import { nbaTeams } from './GroupConfig';

interface EventModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onSubmit: (values: EventDataType, selectedTagIdList: string[]) => Promise<void>;
  onCancel: () => void;
  fetchData: () => void;
}

const AddModal: React.FC<EventModalProps> = ({
  visible,
  setVisible,
  onSubmit,
  onCancel,
  fetchData,
}) => {
  const [form] = Form.useForm();
  const [formTeam1] = Form.useForm();
  const [formTeam2] = Form.useForm();
  const [options, setOptions] = useState<{ value: string; label: string; id: number }[]>([]);
  const [languages, setLanguages] = useState<string[]>(['en']);
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedTagIdList, setSelectedTagIdList] = useState<string[]>([]);
  const [mdValue, setMdValue] = useState('');
  const [team1, setTeam1] = useState<any>(null);
  const [team2, setTeam2] = useState<any>(null);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      const team1Values = await formTeam1.validateFields();
      const team2Values = await formTeam2.validateFields();
      setLoading(true);
      const { start_date, end_date } = formatRangePickerDates(values.date_range);

      const slug: { language: string; content: string }[] = [];
      const title: { language: string; content: string }[] = [];
      const description: { language: string; content: string }[] = [];
      const multimedia_url: { language: string; content: string }[] = [];

      languages.forEach((lang) => {
        slug.push({
          language: lang,
          content: `${team1Values.shortName}_VS_${team2Values.shortName}`,
        });
        title.push({
          language: lang,
          content: `${team1Values.shortName} ${team1Values[`fullName_${lang}`]} VS ${team2Values.shortName} ${team2Values[`fullName_${lang}`]}`,
        });
        description.push({
          language: lang,
          content: `${team1Values[`fullName_${lang}`]} VS ${team2Values[`fullName_${lang}`]}`,
        });
        multimedia_url.push({
          language: lang,
          content: values[`multimedia_url_${lang}`],
        });
      });

      // 清理 rules 中的 markdown 语法
      const cleanedRules = stripMarkdown(mdValue);

      const formattedValues = {
        slug: encodeBase64(JSON.stringify(slug)),
        icon: calculateImageUrl(values, 'icon'),
        image: calculateImageUrl(values, 'image'),
        rules: encodeBase64(cleanedRules),
        neg_risk_market_id: '',
        updated_by: getLocalStorage('userWallet'),
        start_date,
        end_date,
        title: encodeBase64(JSON.stringify(title)),
        description: encodeBase64(JSON.stringify(description)),
        archived: false,
        active: true,
        closed: false,
        multimedia_url: encodeBase64(JSON.stringify(multimedia_url)),
      };

      await onSubmit(formattedValues as any, selectedTagIdList);
      message.success('Sport Event added successfully');
      setLoading(false);
      setVisible(false);
      fetchData();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        message.error(`Add failed: ${error.response?.data?.code || 'Unknown error'}`);
      } else if (error instanceof TypeError) {
        message.error(`Add failed: ${error.message}`);
      } else {
        message.error('Add failed: An unexpected error occurred');
      }

      setLoading(false);
      setVisible(false);
    }
  };
  const handleLanguageChange = (selectedLanguages: string[]) => {
    setLanguages(selectedLanguages);
  };

  const searchTags = async (region: any = languages) => {
    try {
      const response = await fetchTagsListByRegion(region);
      const seenLabels = new Set();

      const newOptions = response.data.tags
        .map((item: { id: number; label: string }) => ({
          value: item.label,
          label: item.label,
          id: item.id,
        }))
        .filter((option: any) => {
          if (seenLabels.has(option.label)) {
            return false;
          } else {
            seenLabels.add(option.label);
            return true;
          }
        });

      setOptions(newOptions);
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  };

  useEffect(() => {
    searchTags(languages);
  }, [languages]);

  const handleTagChange = (selectedIds: any) => {
    const matchedIds = selectedIds
      .map((selectedId: string) => {
        const option = options.find((opt: any) => opt.value === selectedId);
        return option ? option.id : null;
      })
      .filter((id: any) => id !== null);

    setSelectedTagIdList(matchedIds);
  };

  const handleTeamChange = (team: any, setTeam: any, form: any) => {
    const selectedTeam = nbaTeams.find((t) => t.shortName === team);
    setTeam(selectedTeam);
    if (selectedTeam && visible) {
      const fieldsValue: any = {
        shortName: selectedTeam.shortName || '',
        icon: selectedTeam.icon || '',
      };
      languages.forEach((lang) => {
        const langUpper = lang.toUpperCase();
        fieldsValue[`fullName_${lang}`] = (selectedTeam as any)[`fullName${langUpper}`] || '';
        fieldsValue[`description_${lang}`] = (selectedTeam as any)[`description${langUpper}`] || '';
      });
      form.setFieldsValue(fieldsValue);
    }
  };

  return (
    <Modal
      width={800}
      title={<div style={{ marginBottom: '28px' }}>Add Event</div>}
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      <Form form={form} layout="inline" className="w-full justify-center">
        <Row gutter={24}>
          <Col span={12}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <div style={{ height: '32px' }}>Language: </div>
              <Select
                mode="multiple"
                allowClear
                style={{ width: '100%', marginBottom: 16, marginLeft: 16 }}
                placeholder="Please select Languages"
                value={languages}
                onChange={handleLanguageChange}
                options={LANGUAGE_OPTIONS}
              />
            </div>
            <Form.Item
              name="tags"
              label="Tags"
              rules={[{ required: true, message: 'Please select at least one tag' }]}
            >
              <Select
                mode="multiple"
                allowClear
                style={{ width: '100%', marginBottom: 8, marginLeft: 16 }}
                placeholder="Please select Tags"
                value={selectedTagIdList
                  .map((id) => options.find((opt) => opt.id.toString() === id)?.value)
                  .filter(Boolean)}
                onChange={handleTagChange}
                options={options}
              />
            </Form.Item>
            <a
              href="/#/tags"
              target="_blank"
              style={{
                color: '#1890ff',
                marginLeft: 16,
              }}
            >
              if not found, create now
            </a>
            <Divider dashed style={{ margin: '12px 0' }} />
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  name="date_range"
                  label="Date"
                  rules={[{ required: true, message: 'Please select a date range' }]}
                  className="mb-4"
                  layout="vertical"
                >
                  <RangePicker
                    className="w-full p-2 border border-gray-300 rounded"
                    showTime={{ format: 'HH:mm' }}
                    format="YYYY-MM-DD HH:mm"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col
            span={12}
            style={{
              borderLeft: '1px solid #f0f0f0',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Row gutter={24}>
              <Col span={24}>
                {languages.map((language, index) => (
                  <div key={language + '_' + index}>
                    <Form.Item
                      name={`multimedia_url_${language}`}
                      label={`Media_url (${language})`}
                    >
                      <Input />
                    </Form.Item>
                  </div>
                ))}
              </Col>
            </Row>
            <Divider dashed style={{ margin: '12px 0' }} />

            <Row gutter={24} justify="space-around">
              <Col span={12}>
                <Form.Item
                  name="icon"
                  label="Icon"
                  valuePropName="fileList"
                  rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                  layout="vertical"
                >
                  <ImageUpload prefix="event" maxSizeKB={64} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="image"
                  label="Image"
                  valuePropName="fileList"
                  rules={[{ required: import.meta.env.VITE_ENV !== 'dev' }]}
                  layout="vertical"
                >
                  <ImageUpload prefix="event" maxSizeKB={64} />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>

        <Divider dashed style={{ margin: '12px 0' }} />

        <Row gutter={24} style={{ width: '800px' }}>
          <Col span={11} style={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
            <Form form={formTeam1} layout="vertical" style={{ width: '100%', maxWidth: '400px' }}>
              <Select
                placeholder="Select Team 1"
                style={{ width: '100%', marginBottom: 16 }}
                onChange={(value) => handleTeamChange(value, setTeam1, formTeam1)}
                options={nbaTeams.map((team) => ({
                  label: team.fullNameEN,
                  value: team.shortName,
                }))}
              />
              {team1 && (
                <div style={{ display: 'flex', gap: '2px', flexDirection: 'column' }}>
                  <div style={{ textAlign: 'center' }}>
                    <ImageUpload prefix="event" maxSizeKB={64} value={team1.icon} />
                  </div>
                  <Form.Item name="shortName" label="Short Name">
                    <Input />
                  </Form.Item>
                  {languages.map((lang) => (
                    <div key={lang}>
                      <Form.Item
                        name={`fullName_${lang}`}
                        label={`Full Name (${lang.toUpperCase()})`}
                      >
                        <Input />
                      </Form.Item>
                      <Form.Item
                        name={`description_${lang}`}
                        label={`Description (${lang.toUpperCase()})`}
                      >
                        <Input />
                      </Form.Item>
                      <Divider dashed style={{ margin: '12px 0' }} />
                    </div>
                  ))}
                </div>
              )}
            </Form>
          </Col>
          <Col span={2} style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <div className="h-full items-center justify-center">VS</div>
          </Col>
          <Col span={11} style={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
            <Form form={formTeam2} layout="vertical" style={{ width: '100%', maxWidth: '400px' }}>
              <Select
                placeholder="Select Team 2"
                style={{ width: '100%', marginBottom: 16 }}
                onChange={(value) => handleTeamChange(value, setTeam2, formTeam2)}
                options={nbaTeams.map((team) => ({
                  label: team.fullNameEN,
                  value: team.shortName,
                }))}
              />
              {team2 && (
                <div style={{ display: 'flex', gap: '6px', flexDirection: 'column' }}>
                  <div style={{ textAlign: 'center' }}>
                    <ImageUpload prefix="event" maxSizeKB={64} value={team2.icon} />
                  </div>
                  <Form.Item name="shortName" label="Short Name">
                    <Input />
                  </Form.Item>
                  {languages.map((lang) => (
                    <div key={lang}>
                      <Form.Item
                        name={`fullName_${lang}`}
                        label={`Full Name (${lang.toUpperCase()})`}
                      >
                        <Input />
                      </Form.Item>
                      <Form.Item
                        name={`description_${lang}`}
                        label={`Description (${lang.toUpperCase()})`}
                      >
                        <Input />
                      </Form.Item>
                      <Divider dashed style={{ margin: '12px 0' }} />
                    </div>
                  ))}
                </div>
              )}
            </Form>
          </Col>
        </Row>

        <Divider dashed style={{ margin: '12px 0' }} />
        <Row style={{ width: '98%' }}>
          <Col span={24}>
            <Form.Item name="rules" label="Rules" rules={[{ required: true }]}>
              <MarkdownEditor
                value={mdValue}
                onChange={setMdValue}
                disableMarkdown={true}
                needToolBar={false}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default AddModal;
