import { useEffect, useState } from 'react';
import 'tailwindcss/tailwind.css';
import MarkdownEditor from '@/components/MarkdownEditor';

export default function Index() {
  const [markdownContent, setMarkdownContent] = useState('');

  useEffect(() => {
    fetch('/guide.md')
      .then((response) => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.text();
      })
      .then((text) => {
        setMarkdownContent(text);
      })
      .catch((error) => {
        console.error('Error fetching markdown content:', error);
      });
  }, []);

  return (
    <div className="bg-white w-full h-full radius-2xl overflow-hidden shadow-lg">
      <MarkdownEditor value={markdownContent} readOnly needToolBar={false} />
    </div>
  );
}
