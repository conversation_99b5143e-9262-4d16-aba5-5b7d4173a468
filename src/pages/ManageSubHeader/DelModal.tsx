import React from 'react';
import { Modal, Button } from 'antd';

interface DelModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

const DelModal: React.FC<DelModalProps> = ({ visible, setVisible, onConfirm, onCancel }) => {
  return (
    <Modal
      title="Confirm Deletion"
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <Button key="confirm" type="primary" danger onClick={onConfirm}>
          Delete
        </Button>,
      ]}
    >
      <p>Are you sure you want to delete this subheader? This action cannot be undone.</p>
    </Modal>
  );
};

export default DelModal;
