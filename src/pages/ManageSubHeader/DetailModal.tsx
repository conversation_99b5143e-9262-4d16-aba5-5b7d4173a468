import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { Modal, Form, Input, message, Row, Col, Select } from 'antd';
import ImageUpload from '@/components/ImageUpload';
import { calculateImageUrl } from '@/utils';

interface SubHeaderModalProps {
  visible: boolean;
  setVisible: (visible: boolean) => void;
  onSubmit: (values: any) => Promise<void>;
  onCancel: () => void;
  fetchData: () => void;
  record: any;
}

const DetailModal: React.FC<SubHeaderModalProps> = ({
  visible,
  setVisible,
  onSubmit,
  onCancel,
  fetchData,
  record,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    if (record) {
      form.setFieldsValue({
        ...record,
      });
    }
  }, [record, visible]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();

      setLoading(true);
      const formattedValues = {
        ...values,
        image: calculateImageUrl(values, 'image'),
      };
      await onSubmit(formattedValues);

      message.success('SubHeader updated successfully');
      setLoading(false);
      setVisible(false);
      fetchData();
    } catch (error) {
      if (axios.isAxiosError(error)) {
        const errorMessage =
          error.response?.data?.error || error.response?.data?.message || error.message;
        setLoading(false);
        message.error(`Update failed: ${errorMessage}`);
      }
    }
  };

  return (
    <Modal
      width={600}
      title="Edit SubHeader"
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
    >
      <Form form={form} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="label"
              label="Label"
              rules={[{ required: true, message: 'Label is required' }]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="region"
              label="Region"
              rules={[{ required: true, message: 'Region is required' }]}
            >
              <Input />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="slug"
              label="Slug"
              rules={[
                { required: true, message: 'Slug is required' },
                {
                  pattern: /^[a-zA-Z0-9_]+$/,
                  message: 'Only letters, numbers, and underscores are allowed',
                },
              ]}
            >
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="type"
              label="Type"
              rules={[{ required: true, message: 'Type is required' }]}
            >
              <Select
                options={[
                  { value: 'normal', label: 'normal' },
                  { value: 'sport', label: 'sport' },
                ]}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="image"
              label="Image"
              valuePropName="fileList"
              rules={[{ required: import.meta.env.VITE_ENV !== 'dev', message: 'Image is required' }]}
            >
              <ImageUpload prefix="subheader" maxSizeKB={64} value={record?.image} />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default DetailModal;
