import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Space, Image, App, Row, Col, Card, Typography } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  fetchSubHeaderList,
  fetchAddSubHeader,
  fetchEditSubHeader,
  fetchDeleteSubHeader,
} from '@/api/subheader';
import { EventDataType } from '@/types/events';
import Breadcrumb from '@/components/Breadcrumb';
import AddModal from './AddModal';
import DetailModal from './DetailModal';
import DelModal from './DelModal';

const ManageSubHeader: React.FC = () => {
  const [data, setData] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
  const [isDelModalVisible, setIsDelModalVisible] = useState(false);
  const [eventId, setEventId] = useState<number | undefined>(undefined);

  const [currentRecord, setCurrentRecord] = useState<EventDataType | null>(null);

  const { message } = App.useApp();

  const fetchData = useCallback(async () => {
    const result = await fetchSubHeaderList();
    setData(result.data.sub_header_nav);
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleAddSubHeader = async (subHeaderParams: any) => {
    try {
      await fetchAddSubHeader(subHeaderParams);
      message.success('SubHeader added successfully');
      fetchData();
    } catch (error) {
      message.error('Failed to add SubHeader');
    }
  };

  const handleEditSubHeader = async (subHeaderParams: any) => {
    try {
      if (currentRecord?.id !== undefined) {
        await fetchEditSubHeader(currentRecord.id, subHeaderParams);
      } else {
        message.error('Event ID is undefined');
      }
      message.success('SubHeader updated successfully');
      fetchData();
    } catch (error) {
      message.error('Failed to update SubHeader');
    }
  };

  const handleDeleteEvent = async () => {
    try {
      if (eventId !== undefined) {
        await fetchDeleteSubHeader(eventId);
        message.success('SubHeader deleted successfully');
        fetchData();
        setIsDelModalVisible(false);
      }
    } catch (error) {
      message.error('Failed to delete SubHeader');
    }
  };

  const columns: ColumnsType<EventDataType> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Image',
      dataIndex: 'image',
      key: 'image',
      render: (image) => (
        <Image
          src={image}
          alt="image"
          width={40}
          height={40}
          style={{ objectFit: 'cover', objectPosition: 'center' }}
        />
      ),
    },
    {
      title: 'Label',
      dataIndex: 'label',
      key: 'label',
      render: (text) => (
        <Typography.Text strong>
          <div
            style={{
              maxWidth: 200,
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {text}
          </div>
        </Typography.Text>
      ),
    },
    {
      title: 'Region',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: 'Slug',
      dataIndex: 'slug',
      key: 'slug',
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => type || 'N/A',
    },
    {
      title: 'Action',
      key: 'action',
      width: '15%',
      render: (_, record) => (
        <div className="flex gap-2">
          <Button
            onClick={() => {
              setCurrentRecord(record);
              setIsDetailModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Button
            danger
            onClick={() => {
              setEventId(record.id);
              setIsDelModalVisible(true);
            }}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div style={{ overflowX: 'hidden' }}>
      <Breadcrumb url="/#/subheaders" text={'SubHeader'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card title="Sub Header Table">
            <Space style={{ marginBottom: '24px' }}>
              <Button type="primary" onClick={() => setIsModalVisible(true)}>
                Add SubHeader
              </Button>
            </Space>
            <div className="table-responsive" style={{ overflowX: 'auto' }}>
              <Table
                rowKey="id"
                columns={columns}
                dataSource={data}
                pagination={false}
                className="ant-border-space"
              />
            </div>
          </Card>
        </Col>
      </Row>
      <AddModal
        visible={isModalVisible}
        setVisible={setIsModalVisible}
        onSubmit={handleAddSubHeader}
        fetchData={fetchData}
        onCancel={() => setIsModalVisible(false)}
      />
      <DetailModal
        visible={isDetailModalVisible}
        setVisible={setIsDetailModalVisible}
        onSubmit={handleEditSubHeader}
        fetchData={fetchData}
        onCancel={() => setIsDetailModalVisible(false)}
        record={currentRecord}
      />
      <DelModal
        visible={isDelModalVisible}
        setVisible={setIsDelModalVisible}
        onConfirm={handleDeleteEvent}
        onCancel={() => setIsDelModalVisible(false)}
      />
    </div>
  );
};

export default ManageSubHeader;
