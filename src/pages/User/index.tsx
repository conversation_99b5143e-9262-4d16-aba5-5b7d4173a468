import { Table, theme } from 'antd';
import AvaForm from './AvaForm';
import { columns, data } from './coloum';

export default function User() {
  const { token } = theme.useToken();

  const listStyle: React.CSSProperties = {
    background: token.colorFillAlter,
    borderRadius: token.borderRadiusLG,
    padding: 12,
  };

  return (
    <main>
      <div>
        <AvaForm />
        <div style={listStyle}>
          <h3>{'userList'}</h3>
          <Table columns={columns} dataSource={data} pagination={{ pageSize: 10 }} />
        </div>
      </div>
    </main>
  );
}
