import { Space, Tag, type TableProps, Modal, message, App } from 'antd';

interface DataType {
  id: number;
  key: string;
  name: string;
  role: number;
  tags: string[];
}

const DeleteModal = ({ record, onOk }: { record: any; onOk: () => void }) => (
  <Modal
    title="确认删除"
    open={true}
    onOk={onOk}
    onCancel={() => Modal.destroyAll()}
    okText="确认"
    cancelText="取消"
  >
    确定要删除记录 {record.name} 吗？
  </Modal>
);

const handleDelete = (record: any) => {
  const onOk = () => {
    console.log('删除记录:', record);
    message.success(`删除记录: ${record.key}`);
    Modal.destroyAll();
  };

  const ConfirmModal = () => {
    Modal.confirm({
      content: <DeleteModal record={record} onOk={onOk} />,
      icon: null,
      okButtonProps: { style: { display: 'none' } },
      cancelButtonProps: { style: { display: 'none' } },
    });
  };

  ConfirmModal();
};

const columns: TableProps<DataType>['columns'] = [
  {
    title: '用户id',
    dataIndex: 'id',
    key: 'id',
    render: (text) => <a>{text}</a>,
    fixed: 'left',
  },
  {
    title: '用户名',
    dataIndex: 'name',
    key: 'name',
    render: (text) => <a>{text}</a>,
    fixed: 'left',
  },
  {
    title: '角色',
    dataIndex: 'role',
    key: 'role',
    render: (role) => (role === 1 ? '超级管理员' : '开发者'),
  },
  {
    title: '标签',
    key: 'tags',
    dataIndex: 'tags',
    render: (_, { tags }) => (
      <>
        {tags.map((tag) => {
          let color = tag.length > 5 ? 'geekblue' : 'green';
          if (tag === 'loser') {
            color = 'volcano';
          }
          return (
            <Tag color={color} key={tag}>
              {tag.toUpperCase()}
            </Tag>
          );
        })}
      </>
    ),
  },
  {
    title: '操作',
    key: 'action',
    render: (_, record) => (
      <Space size="middle">
        <a>编辑</a>
        <a onClick={() => handleDelete(record)}>删除</a>
      </Space>
    ),
  },
];

const data: DataType[] = [
  {
    id: 1,
    key: '1',
    name: 'Georage',
    role: 1,
    tags: ['后端工程师', 'developer'],
  },
  { id: 2, key: '2', name: 'Ben', role: 2, tags: ['产品经理'] },
  { id: 3, key: '3', name: 'Rick', role: 2, tags: ['前端工程师', 'developer'] },
  { id: 4, key: '4', name: 'Tom', role: 2, tags: ['Next-Admin'] },
  { id: 5, key: '5', name: 'Json', role: 2, tags: ['Next-Admin'] },
];

export { columns, data };
