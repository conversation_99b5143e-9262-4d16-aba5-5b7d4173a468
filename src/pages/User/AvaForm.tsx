import { Button, Col, Form, Input, Row, Select, Space } from 'antd';

const { Option } = Select;

const searchLabels = ['用户名', '角色'];

const AdvancedSearchForm = () => {
  const [form] = Form.useForm();

  const getFields = () => {
    return searchLabels.map((label, i) => (
      <Col span={8} key={i}>
        <Form.Item
          name={`field-${i}`}
          label={label}
          rules={[{ required: false, message: '请输入内容' }]}
        >
          {label === '角色' ? (
            <Select placeholder="请选择角色">
              <Option value="超级开发者">超级开发者</Option>
              <Option value="开发者">开发者</Option>
              <Option value="用户">用户</Option>
            </Select>
          ) : (
            <Input placeholder="请输入关键词" />
          )}
        </Form.Item>
      </Col>
    ));
  };

  const onFinish = (values: any) => {
    console.log('Received values of form: ', values);
  };

  return (
    <Form form={form} name="advanced_search" onFinish={onFinish}>
      <Row gutter={24}>
        {getFields()}
        <Col span={8} style={{ textAlign: 'right' }}>
          <Space size="small">
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
            <Button onClick={() => form.resetFields()}>清空</Button>
            {/* <a style={{ fontSize: 12 }} onClick={() => setExpand(!expand)}>
              <DownOutlined rotate={expand ? 180 : 0} /> {expand ? '收起' : '展开'}
            </a> */}
          </Space>
        </Col>
      </Row>
    </Form>
  );
};

export default AdvancedSearchForm;
