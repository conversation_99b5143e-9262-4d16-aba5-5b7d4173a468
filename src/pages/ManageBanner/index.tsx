import React, { useState, useEffect, useCallback } from 'react';
import { Table, Image, App, Row, Col, Card, Tabs, Tag, Button, Space, Select, Badge } from 'antd';
import { LinkOutlined, GlobalOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { fetchBannerAllLanguagesSeparated } from '@/api/banner';
import { BannerRawDataType, BannerLanguage } from '@/types/banner';
import { BANNER_LANGUAGES } from '@/constants/banner';
import { formatDate } from '@/utils';
import Breadcrumb from '@/components/Breadcrumb';
import AddModal from './AddModal';
import EditModal from './EditModal';
import DeleteModal from './DeleteModal';

const ManageBanner: React.FC = () => {
  const [bannerData, setBannerData] = useState<{ [key in BannerLanguage]: BannerRawDataType[] }>({
    en: [],
    zh: [],
    ko: [],
  });
  const [loading, setLoading] = useState<boolean>(false);

  // Modal状态管理
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<BannerRawDataType | null>(null);
  const [currentLanguage, setCurrentLanguage] = useState<BannerLanguage>('en');
  const [featureFilter, setFeatureFilter] = useState<'all' | 'true' | 'false'>('all');

  const { message } = App.useApp();

  // 跳转到图片上传网站
  const handleGetImageLink = () => {
    window.open('https://postimages.org/', '_blank');
  };

  // Feature过滤处理
  const handleFeatureFilterChange = (value: 'all' | 'true' | 'false') => {
    setFeatureFilter(value);
  };

  // 根据Feature过滤数据
  const getFilteredData = (data: BannerRawDataType[]) => {
    if (featureFilter === 'all') {
      return data;
    }
    const isFeature = featureFilter === 'true';
    return data.filter((item) => item.feature === isFeature);
  };

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const result = await fetchBannerAllLanguagesSeparated();
      const data = result.data || { en: [], zh: [], ko: [] };
      setBannerData(data);
    } catch (error) {
      message.error('Failed to fetch banner data');
      console.error('Error fetching banner data:', error);
      setBannerData({ en: [], zh: [], ko: [] });
    } finally {
      setLoading(false);
    }
  }, [message]);

  useEffect(() => {
    // 组件加载时自动获取数据
    fetchData();
  }, [fetchData]);

  // 创建单语言表格的列定义
  const createColumns = (): ColumnsType<BannerRawDataType> => [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: '8%',
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: 'Title',
      dataIndex: 'title',
      key: 'title',
      width: '30%',
      render: (text) => (
        <div className=" font-semibold" style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>
          {text}
        </div>
      ),
    },
    {
      title: 'Sub Title',
      dataIndex: 'sub_title',
      key: 'sub_title',
      width: '15%',
      render: (text) => (
        <div style={{ wordBreak: 'break-word', whiteSpace: 'pre-wrap' }}>{text}</div>
      ),
    },
    {
      title: 'Image',
      dataIndex: 'image_url',
      key: 'image_url',
      width: '15%',
      render: (text) => (
        <Image
          width={50}
          height={50}
          src={text}
          style={{ objectFit: 'cover', borderRadius: '4px' }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
        />
      ),
    },
    {
      title: 'Event ID',
      dataIndex: 'event_id',
      key: 'event_id',
      width: '12%',
    },
    {
      title: 'Feature',
      dataIndex: 'feature',
      key: 'feature',
      width: '10%',
      render: (value: boolean) => (
        <Tag color={value ? 'green' : 'default'}>{value ? 'Featured' : 'Normal'}</Tag>
      ),
    },
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: '12%',
      render: (text) => text && formatDate(text),
      sorter: (a, b) => {
        const dateA = a.timestamp ? new Date(a.timestamp).getTime() : 0;
        const dateB = b.timestamp ? new Date(b.timestamp).getTime() : 0;
        return dateA - dateB;
      },
    },
    {
      title: 'Action',
      key: 'action',
      width: '15%',
      render: (_, record) => (
        <div className="flex gap-2">
          <Button
            onClick={() => {
              setCurrentRecord(record);
              setIsEditModalVisible(true);
            }}
          >
            Edit
          </Button>
          <Button
            danger
            onClick={() => {
              setCurrentRecord(record);
              setIsDeleteModalVisible(true);
            }}
          >
            Delete
          </Button>
        </div>
      ),
    },
  ];

  // 渲染单个语言的表格
  const renderLanguageTable = (language: BannerLanguage, data: BannerRawDataType[]) => (
    <div>
      <div style={{ marginBottom: '16px' }}>
        <Space>
          <Button
            type="primary"
            onClick={() => {
              setCurrentLanguage(language);
              setIsAddModalVisible(true);
            }}
          >
            Add Banner
          </Button>
          <Button
            type="default"
            icon={<LinkOutlined />}
            onClick={handleGetImageLink}
            style={{
              borderColor: '#1890ff',
              color: '#1890ff',
            }}
          >
            Get Image Link
          </Button>
          <Select
            value={featureFilter}
            onChange={handleFeatureFilterChange}
            style={{ width: 120 }}
            placeholder="Filter by Feature"
          >
            <Select.Option value="all">All</Select.Option>
            <Select.Option value="true">Featured</Select.Option>
            <Select.Option value="false">Normal</Select.Option>
          </Select>
        </Space>
      </div>
      <div className="table-responsive" style={{ overflowX: 'auto' }}>
        <Table
          columns={createColumns()}
          dataSource={getFilteredData(data)}
          loading={loading}
          pagination={false}
          className="ant-border-space"
          rowKey="id"
        />
      </div>
    </div>
  );

  // 创建Tab项
  const tabItems = BANNER_LANGUAGES.map(({ value: lang, label }) => {
    const count = bannerData[lang].length;
    const featuredCount = bannerData[lang].filter((item) => item.feature).length;

    return {
      key: lang,
      label: (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px', padding: '4px 8px' }}>
          <span style={{ fontWeight: 500 }}>{label}</span>
          <Badge
            count={count}
            style={{
              backgroundColor: '#1890ff',
              fontSize: '11px',
              minWidth: '18px',
              height: '18px',
              lineHeight: '18px',
            }}
          />
          {featuredCount > 0 && (
            <Badge
              count={featuredCount}
              style={{
                backgroundColor: '#52c41a',
                fontSize: '10px',
                minWidth: '16px',
                height: '16px',
                lineHeight: '16px',
              }}
              title={`${featuredCount} featured banners`}
            />
          )}
        </div>
      ),
      children: renderLanguageTable(lang, bannerData[lang]),
    };
  });

  return (
    <div style={{ overflowX: 'hidden' }}>
      <Breadcrumb url="/#/banner" text={'Banner'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <GlobalOutlined style={{ color: '#1890ff', fontSize: '18px' }} />
                <span style={{ fontSize: '16px', fontWeight: 600 }}>Banner</span>
              </div>
            }
            style={{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}
          >
            <Tabs
              defaultActiveKey="en"
              items={tabItems}
              size="large"
              type="card"
              tabBarStyle={{
                marginBottom: '16px',
                background: '#f8f9fa',
                padding: '8px',
                borderRadius: '8px',
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* Modal组件 */}
      <AddModal
        visible={isAddModalVisible}
        onCancel={() => setIsAddModalVisible(false)}
        fetchData={fetchData}
        language={currentLanguage}
      />

      <EditModal
        visible={isEditModalVisible}
        onCancel={() => setIsEditModalVisible(false)}
        fetchData={fetchData}
        record={currentRecord}
      />

      <DeleteModal
        visible={isDeleteModalVisible}
        onCancel={() => setIsDeleteModalVisible(false)}
        fetchData={fetchData}
        record={currentRecord}
      />
    </div>
  );
};

export default ManageBanner;
