import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, message, Radio } from 'antd';
import { BannerRawDataType } from '@/types/banner';
import { fetchUpdateBanner } from '@/api/banner';

interface EditModalProps {
  visible: boolean;
  onCancel: () => void;
  fetchData: () => void;
  record: BannerRawDataType | null;
}

const EditModal: React.FC<EditModalProps> = ({ visible, onCancel, fetchData, record }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (visible && record) {
      form.setFieldsValue({
        title: record.title,
        sub_title: record.sub_title,
        event_id: record.event_id,
        feature: record.feature,
        image_url: record.image_url, // 直接设置URL字符串
      });
    }
  }, [visible, record, form]);

  const handleOk = async () => {
    if (!record) return;

    try {
      const values = await form.validateFields();
      setLoading(true);

      const bannerParams = {
        title: values.title,
        sub_title: values.sub_title,
        event_id: parseInt(values.event_id), // 确保是整数
        feature: values.feature === 'true' || values.feature === true, // 转换为boolean
        image_url: values.image_url, // 直接使用URL值
      };

      await fetchUpdateBanner(record.id, bannerParams);
      message.success('Banner updated successfully');
      fetchData();
      onCancel();
    } catch (error) {
      message.error('Failed to update banner');
      console.error('Error updating banner:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`Edit Banner - ${record?.region?.toUpperCase()}`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="title"
          label="Title"
          rules={[{ required: true, message: 'Please enter the title' }]}
        >
          <Input placeholder="Enter banner title" />
        </Form.Item>

        <Form.Item name="sub_title" label="Sub Title">
          <Input placeholder="Enter banner sub title" />
        </Form.Item>

        <Form.Item
          name="event_id"
          label="Event ID"
          rules={[{ required: true, message: 'Please enter the event ID' }]}
        >
          <Input placeholder="Enter event ID" />
        </Form.Item>

        <Form.Item name="feature" label="Is Show">
          <Radio.Group>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="image_url"
          label="Banner Image URL"
          rules={[
            { required: true, message: 'Please enter the banner image URL' },
            { type: 'url', message: 'Please enter a valid URL' },
          ]}
        >
          <Input
            placeholder="Enter banner image URL (e.g., https://example.com/image.jpg)"
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditModal;
