import React, { useState } from 'react';
import { Modal, Typography, message, Divider, Space, Tag } from 'antd';
import { ExclamationCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import { BannerRawDataType } from '@/types/banner';
import { fetchDeleteBanner } from '@/api/banner';

const { Text } = Typography;

interface DeleteModalProps {
  visible: boolean;
  onCancel: () => void;
  fetchData: () => void;
  record: BannerRawDataType | null;
}

const DeleteModal: React.FC<DeleteModalProps> = ({ visible, onCancel, fetchData, record }) => {
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    if (!record) return;

    try {
      setLoading(true);
      await fetchDeleteBanner(record.id);
      message.success('Banner deleted successfully');
      fetchData();
      onCancel();
    } catch (error) {
      message.error('Failed to delete banner');
      console.error('Error deleting banner:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <DeleteOutlined style={{ color: '#ff4d4f', fontSize: '20px' }} />
          <span style={{ fontSize: '16px', fontWeight: 600 }}>Delete Banner</span>
        </div>
      }
      open={visible}
      onOk={handleOk}
      onCancel={onCancel}
      confirmLoading={loading}
      okText="Delete Banner"
      okType="danger"
      cancelText="Cancel"
      width={520}
      centered
    >
      <div style={{ padding: '20px 0' }}>
        <div style={{ textAlign: 'center', marginBottom: '24px' }}>
          <ExclamationCircleOutlined
            style={{
              fontSize: '48px',
              color: '#faad14',
              marginBottom: '16px',
              display: 'block',
            }}
          />
          <Text style={{ fontSize: '16px', color: '#262626' }}>
            Are you sure you want to delete this banner?
          </Text>
        </div>

        {record && (
          <>
            <Divider style={{ margin: '16px 0' }} />
            <div
              style={{
                padding: '16px',
                backgroundColor: '#fafafa',
                borderRadius: '8px',
                border: '1px solid #f0f0f0',
              }}
            >
              <Space direction="vertical" size="small" style={{ width: '100%' }}>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Text strong style={{ color: '#595959' }}>
                    ID:
                  </Text>
                  <Tag color="blue">{record.id}</Tag>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Text strong style={{ color: '#595959' }}>
                    Title:
                  </Text>
                  <Text
                    style={{ maxWidth: '200px', textAlign: 'right' }}
                    ellipsis={{ tooltip: record.title }}
                  >
                    {record.title}
                  </Text>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Text strong style={{ color: '#595959' }}>
                    Language:
                  </Text>
                  <Tag color="green">{record.region?.toUpperCase()}</Tag>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Text strong style={{ color: '#595959' }}>
                    Event ID:
                  </Text>
                  <Tag color="orange">{record.event_id}</Tag>
                </div>
                <div
                  style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}
                >
                  <Text strong style={{ color: '#595959' }}>
                    Featured:
                  </Text>
                  <Tag color={record.feature ? 'green' : 'default'}>
                    {record.feature ? '✓ Featured' : '✗ Normal'}
                  </Tag>
                </div>
              </Space>
            </div>
          </>
        )}

        <div
          style={{
            marginTop: '20px',
            padding: '12px',
            backgroundColor: '#fff2e8',
            borderRadius: '6px',
            border: '1px solid #ffec3d',
            textAlign: 'center',
          }}
        >
          <Text type="warning" style={{ fontSize: '14px', fontWeight: 500 }}>
            ⚠️ This action cannot be undone
          </Text>
        </div>
      </div>
    </Modal>
  );
};

export default DeleteModal;
