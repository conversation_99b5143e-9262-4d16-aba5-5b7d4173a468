import React, { useState } from 'react';
import { Modal, Form, Input, message, Radio } from 'antd';
import dayjs from 'dayjs';
import { BannerLanguage } from '@/types/banner';
import { fetchAddBanner } from '@/api/banner';

interface AddModalProps {
  visible: boolean;
  onCancel: () => void;
  fetchData: () => void;
  language: BannerLanguage;
}

const AddModal: React.FC<AddModalProps> = ({ visible, onCancel, fetchData, language }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      const bannerParams = {
        event_id: parseInt(values.event_id), // 确保是整数
        feature: values.feature === 'true' || values.feature === true, // 转换为boolean
        image_url: values.image_url, // 直接使用URL值
        region: language, // 使用当前语言作为region
        sub_title: values.sub_title, // 恢复正确的字段名
        title: values.title,
        timestamp: dayjs.utc().toISOString(),
      };

      await fetchAddBanner(bannerParams);
      message.success('Banner added successfully');
      fetchData();
      onCancel();
      form.resetFields();
    } catch (error) {
      message.error('Failed to add banner');
      console.error('Error adding banner:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`Add Banner - ${language.toUpperCase()}`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
    >
      <Form form={form} layout="vertical">
        <Form.Item
          name="title"
          label="Title"
          rules={[{ required: true, message: 'Please enter the title' }]}
        >
          <Input placeholder="Enter banner title" />
        </Form.Item>

        <Form.Item name="sub_title" label="Sub Title">
          <Input placeholder="Enter banner sub title" />
        </Form.Item>

        <Form.Item
          name="event_id"
          label="Event ID"
          rules={[{ required: true, message: 'Please enter the event ID' }]}
        >
          <Input placeholder="Enter event ID" />
        </Form.Item>

        <Form.Item name="feature" label="Is Show" initialValue={false}>
          <Radio.Group>
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          name="image_url"
          label="Banner Image URL"
          rules={[
            { required: true, message: 'Please enter the banner image URL' },
            { type: 'url', message: 'Please enter a valid URL' },
          ]}
        >
          <Input
            placeholder="Enter banner image URL (e.g., https://example.com/image.jpg)"
            style={{ width: '100%' }}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default AddModal;
