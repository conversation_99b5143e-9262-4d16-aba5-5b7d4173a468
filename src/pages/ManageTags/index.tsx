import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Table, Input, Button, Space, Card, Row, Col, Tag, Typography } from 'antd';
import TagModal from './TagModal';
import type { ColumnsType } from 'antd/es/table';
import Breadcrumb from '@/components/Breadcrumb';
import { formatDate, uniqueById, TAGCOLORLIST } from '@/utils';
import { fetchTagsList, fetchTagsListBySearch } from '@/api/tags';

interface TagDataType {
  id: number;
  key: string;
  label: string;
  slug: string;
  updated_at: string;
  created_at: string;
}
const colorMap = new Map();

const ManageTags: React.FC = () => {
  const [searchText, setSearchText] = useState('');
  const [filteredData, setFilteredData] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);

  const fetchData = useCallback(async () => {
    try {
      const result = await fetchTagsList();
      const tags = result.data.tags.map((tag: any) => ({
        ...tag,
        key: tag.id,
      }));

      const sortedTags = tags.sort(
        (a: any, b: any) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime()
      );

      const uniqueTags = uniqueById(sortedTags);
      setFilteredData(uniqueTags);
    } catch (error) {
      console.error('Error fetching tags:', error);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const handleSearch = useCallback(async () => {
    try {
      const result =
        searchText.trim() === '' ? await fetchTagsList() : await fetchTagsListBySearch(searchText);
      const tags = result.data.tags.map((tag: any) => ({
        ...tag,
        key: tag.id,
      }));
      const uniqueTags = uniqueById(tags);
      setFilteredData(uniqueTags);
    } catch (error) {
      console.error('Error fetching tags by search:', error);
    }
  }, [searchText]);

  const columns: ColumnsType<TagDataType> = useMemo(
    () => [
      {
        title: 'Tag ID',
        dataIndex: 'id',
        key: 'id',
        sorter: (a, b) => a.id - b.id,
      },
      {
        title: 'Label',
        dataIndex: 'label',
        key: 'label',
        render: (text) => <Typography.Text strong>{text}</Typography.Text>,
      },
      {
        title: 'Slug',
        dataIndex: 'slug',
        key: 'slug',
      },

      {
        title: 'Region',
        dataIndex: 'region',
        key: 'region',
        render: (text) =>
          text && (
            <>
              {text.split(',').map((region: string) => {
                if (!colorMap.has(region)) {
                  colorMap.set(region, TAGCOLORLIST[colorMap.size % TAGCOLORLIST.length]);
                }
                return (
                  <Tag color={colorMap.get(region)} key={region}>
                    {region}
                  </Tag>
                );
              })}
            </>
          ),
      },
      {
        title: 'Created Time',
        dataIndex: 'created_at',
        key: 'created_at',
        render: (text) => formatDate(text),
        sorter: (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
      },
      {
        title: 'Updated Time',
        dataIndex: 'updated_at',
        key: 'updated_at',
        render: (text) => formatDate(text),
        sorter: (a, b) => new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
      },
    ],
    []
  );

  return (
    <div className="overflow-hidden">
      <Breadcrumb url="/#/tags" text={'Tag'} />
      <Row gutter={[24, 0]}>
        <Col span={24}>
          <Card title="Tags Table">
            <Space style={{ marginBottom: '24px' }}>
              <Input
                placeholder="Search tags"
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                className="w-64"
              />
              <Button type="primary" onClick={handleSearch}>
                Search
              </Button>
              <Button type="primary" onClick={() => setIsModalVisible(true)}>
                Add Tag
              </Button>
            </Space>
            <div className="table-responsive">
              <Table
                columns={columns}
                dataSource={filteredData}
                pagination={false}
                className="ant-border-space"
              />
            </div>
          </Card>
        </Col>
      </Row>
      <TagModal
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        fetchData={fetchData}
      />
    </div>
  );
};

export default ManageTags;
