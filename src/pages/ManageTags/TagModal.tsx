import React, { useState } from 'react';
import { Modal, Form, Input, message, Select } from 'antd';
import { LANGUAGE_OPTIONS } from '@/utils';
import { fetchAddTag } from '@/api/tags';

interface TagModalProps {
  visible: boolean;
  onCancel: () => void;
  fetchData: () => void;
}

const TagModal: React.FC<TagModalProps> = ({ visible, onCancel, fetchData }) => {
  const [form] = Form.useForm();
  const [labelLength, setLabelLength] = useState(0);
  const [slugLength, setSlugLength] = useState(0);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (values.region) {
        values.region = values.region.join(',');
      }

      await fetchAddTag({ ...values, sync: false, force_hide: false, force_show: false });
      message.success('add tag success');
      fetchData();
      onCancel();
      form.resetFields();
    } catch (error) {
      message.error('add tag failed');
    }
  };

  return (
    <Modal title="Add Tag" open={visible} onOk={handleOk} onCancel={onCancel} className="p-4">
      <Form form={form} layout="vertical" className="space-y-4">
        <Form.Item
          name="region"
          label="Region"
          rules={[{ required: true, message: 'Please select a region!' }]}
        >
          <Select mode="multiple" options={LANGUAGE_OPTIONS} />
        </Form.Item>
        <Form.Item
          name="label"
          label="Tag Label"
          rules={[{ required: true, message: 'Please enter the label' }]}
          className="mb-4"
        >
          <Input
            className="w-full p-2 border border-gray-300 rounded"
            maxLength={30}
            onChange={(e) => setLabelLength(e.target.value.length)}
            suffix={`${labelLength}/30`}
          />
        </Form.Item>
        <Form.Item
          name="slug"
          label="Tag Slug"
          rules={[{ required: true, message: 'Please enter the slug' }]}
          className="mb-4"
        >
          <Input
            className="w-full p-2 border border-gray-300 rounded"
            maxLength={30}
            onChange={(e) => setSlugLength(e.target.value.length)}
            suffix={`${slugLength}/30`}
          />
        </Form.Item>
        {/* <Form.Item
          label="Sync"
          name="sync"
          rules={[{ required: true, message: 'sync' }]}
          className="mb-4"
        >
          <Radio.Group className="flex space-x-4">
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Force Hide"
          name="force_hide"
          rules={[{ required: true, message: 'Please choose whether to force hiding' }]}
          className="flex items-center mb-4"
        >
          <Radio.Group className="flex space-x-4">
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item
          label="Force Show"
          name="force_show"
          rules={[{ required: true, message: 'Please choose whether to force show' }]}
          className="flex items-center mb-4"
        >
          <Radio.Group className="flex space-x-4">
            <Radio value={true}>Yes</Radio>
            <Radio value={false}>No</Radio>
          </Radio.Group>
        </Form.Item> */}
      </Form>
    </Modal>
  );
};

export default TagModal;
