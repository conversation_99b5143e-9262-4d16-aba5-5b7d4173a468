import { App, ConfigProvider } from 'antd';
import { useRoutes } from 'react-router-dom';
import routes from './router';
import AuthRouter from './router/AuthRouter';
import enUS from 'antd/lib/locale/en_US';

const RootApp = () => {
  const element = useRoutes(routes as any);
  return (
    <App>
      <ConfigProvider locale={enUS}>
        <AuthRouter>{element}</AuthRouter>
      </ConfigProvider>
    </App>
  );
};

export default RootApp;
