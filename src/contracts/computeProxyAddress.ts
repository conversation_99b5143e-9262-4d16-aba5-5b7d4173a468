import { Contract, JsonRpcProvider, getAddress } from 'ethers'; // 修正 utils 引用
import Papa from 'papapar<PERSON>';

// 根据环境获取链配置
const getChainConfig = () => {
  const isDev = import.meta.env.VITE_ENV === 'dev';

  if (isDev) {
    // 开发环境 - Sepolia
    const devFactoryAddress =
      import.meta.env.VITE_DEV_FACTORY_ADDRESS || '******************************************';
    return {
      providerUrl: 'https://ethereum-sepolia-rpc.publicnode.com',
      chainId: 11155111, // Sepolia chain ID
      proxyFactoryAddress: devFactoryAddress, // Sepolia factory address from env
      networkName: 'Sepolia',
    };
  } else {
    // 生产环境 - Base
    const factoryAddress = import.meta.env.VITE_FACTORY_ADDRESS;
    if (!factoryAddress) {
      throw new Error('VITE_FACTORY_ADDRESS is not defined for production environment');
    }

    // 尝试多个 Base RPC 端点
    const baseRpcUrls = [
      import.meta.env.VITE_PROVIDER_URL,
      'https://mainnet.base.org',
      'https://base-mainnet.public.blastapi.io',
      'https://base.gateway.tenderly.co',
    ].filter(Boolean);

    return {
      providerUrl: baseRpcUrls[0] || 'https://mainnet.base.org', // Base mainnet RPC
      chainId: 8453, // Base chain ID
      proxyFactoryAddress: factoryAddress, // Base factory address from env
      networkName: 'Base',
      fallbackUrls: baseRpcUrls.slice(1), // 备用 RPC 端点
    };
  }
};

const abi = [
  'function computeProxyAddress(address user) public view returns (address)',
  'function getProxyAddress(address user) public view returns (address)', // 备用函数名
  'function proxyFor(address user) public view returns (address)', // 另一个可能的函数名
];

// 延迟函数
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// 重试函数
async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000,
  maxDelay: number = 4000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error: any) {
      lastError = error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      // 计算延迟时间（指数退避）
      const delayTime = Math.min(initialDelay * Math.pow(2, attempt), maxDelay);
      await delay(delayTime);
    }
  }

  throw lastError!;
}

async function computeProxyAddress(address: string): Promise<string> {
  const chainConfig = getChainConfig();

  try {
    const result = await retryWithBackoff(
      async () => {
        // 尝试多个 RPC 端点
        const urls = [chainConfig.providerUrl, ...(chainConfig.fallbackUrls || [])];
        let lastError: Error | null = null;

        for (const url of urls) {
          if (!url) continue;

          try {
            const provider = new JsonRpcProvider(url);
            const contract = new Contract(chainConfig.proxyFactoryAddress, abi, provider);
            const result = await contract.computeProxyAddress(address);

            // 检查结果是否有效
            if (!result || result === '0x0000000000000000000000000000000000000000') {
              throw new Error(`Invalid proxy address returned: ${result}`);
            }

            return result;
          } catch (error: any) {
            lastError = error;
            continue;
          }
        }

        // 所有 RPC 都失败了
        throw lastError || new Error('All RPC endpoints failed');
      },
      3,
      1000,
      4000
    );

    return result;
  } catch (error: any) {
    return '';
  }
}

async function computeProxyAddresses(addresses: string[]): Promise<string[]> {
  try {
    // 校验和格式化地址
    const formattedAddresses = addresses
      .map((address) => {
        try {
          return getAddress(address.toLowerCase()); // 使用 getAddress 校验和格式化地址
        } catch (error) {
          return null; // 跳过无效地址
        }
      })
      .filter((address) => address !== null); // 过滤掉无效地址

    // 使用改进后的单个地址计算函数，它已经包含了重试机制
    const results = await Promise.all(
      formattedAddresses.map(async (address) => {
        const result = await computeProxyAddress(address!);
        return result;
      })
    );

    return results;
  } catch (error) {
    return [];
  }
}

async function computeProxyAddressesFromCsv(filePath: string): Promise<{ [key: string]: string }> {
  const addresses: string[] = [];
  const results: { [key: string]: string } = {};

  return new Promise((resolve, reject) => {
    Papa.parse(filePath, {
      download: true, // 允许从文件路径下载 CSV 文件
      header: true,
      skipEmptyLines: true,
      complete: async (parseResult: Papa.ParseResult<{ address: string }>) => {
        try {
          parseResult.data.forEach((row) => {
            if (row.address) {
              try {
                addresses.push(getAddress(row.address.toLowerCase())); // 使用 getAddress 校验和格式化地址
              } catch (error) {
                console.error(`Invalid address in CSV skipped: ${row.address}`);
              }
            }
          });

          const proxyAddresses = await computeProxyAddresses(addresses);
          addresses.forEach((address, index) => {
            results[address] = proxyAddresses[index];
          });
          resolve(results);
        } catch (error) {
          reject(error);
        }
      },
      error: (error) => {
        reject(error);
      },
    });
  });
}

export { computeProxyAddress, computeProxyAddresses, computeProxyAddressesFromCsv };
