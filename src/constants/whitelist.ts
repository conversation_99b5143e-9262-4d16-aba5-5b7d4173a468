/**
 * 前端白名单配置
 * 用于管理用户角色权限，避免暴露后端用户角色表
 */

// 从环境变量读取管理员钱包地址白名单
const getAdminWhitelist = (): string[] => {
  const adminAddresses = import.meta.env.VITE_ADMIN_WHITELIST;
  if (!adminAddresses) {
    return [];
  }
  // 支持逗号分隔的多个地址，并转换为小写
  return adminAddresses.split(',').map((addr: string) => addr.trim().toLowerCase());
};

export const ADMIN_WHITELIST: string[] = getAdminWhitelist();

/**
 * 根据钱包地址获取用户角色
 * @param walletAddress 钱包地址
 * @returns 'admin' | 'event_writer'
 */
export const getUserRoleByWallet = (walletAddress: string): 'admin' | 'event_writer' => {
  if (!walletAddress) {
    return 'event_writer';
  }

  const normalizedAddress = walletAddress.toLowerCase();
  if (ADMIN_WHITELIST.includes(normalizedAddress)) {
    return 'admin';
  }

  // 默认返回 event_writer
  return 'event_writer';
};

/**
 * 检查钱包地址是否为管理员
 * @param walletAddress 钱包地址
 * @returns boolean
 */
export const isAdmin = (walletAddress: string): boolean => {
  return getUserRoleByWallet(walletAddress) === 'admin';
};
