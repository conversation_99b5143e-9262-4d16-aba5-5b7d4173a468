export type BannerLanguage = 'en' | 'zh' | 'ko';

// 单个语言的Banner数据（从API返回的原始格式）
export type BannerRawDataType = {
  id: number;
  event_id: number; // 数据库中是integer
  feature: boolean; // 数据库中是boolean
  image_url: string;
  region: string; // 这里的region实际上是语言代码
  sub_title: string;
  timestamp: string;
  title: string;
};

// 合并后的多语言Banner数据（用于前端显示）
export type BannerDataType = {
  id: number;
  event_id: string;
  feature: string;
  image_url: string;
  timestamp: string;
  title: { [key in BannerLanguage]?: string };
  sub_title: { [key in BannerLanguage]?: string };
};

export type BannerParams = {
  region: string; // 实际上是语言代码
};
