// 必填项
export type RequiredFields = {
  slug: string; // 口号
  title: string; // 标题
  icon: string; // 图标
  image: string; // 图片,可与 icon 相同
  description: string; // 描述
  start_date: string; // 开始日期，格式 ISO 8601 YYYY-MM-DDTHH:MM:SS.SSSSSS
  end_date: string; // 结束日期，例如：2024-12-27T04:39:22.503618
  rules: string; // 规则
  active: boolean; // 是否激活
  closed: boolean; // 是否关闭
};

// 选填项
export type OptionalFields = CommonOptionalFields & UncommonOptionalFields;
// 常用
export type CommonOptionalFields = {
  new?: boolean; // 是否为新活动
  featured?: boolean; // 是否为特色活动，展示角标
  comments_enabled?: boolean; // 是否开启评论
  enable_order_book?: boolean; // 是否展示 order_book
  sync?: boolean; // 是否同步
  show_all_outcomes?: boolean; // 是否展示所有结果
};

// 不常用
type UncommonOptionalFields = {
  ticker?: string; // 精度
  sort_by?: string; // 排序
  featured_image?: string; // 特色图片
  resolution_source?: string; // 解析来源
  gmp_chart_mode?: string; // GMP 图表模式

  cyom?: boolean; // 是否自定义市场
  neg_risk?: boolean; // 是否负风险
  archived?: boolean; // 是否归档
  restricted?: boolean; // 是否受限
  enable_neg_risk?: boolean; // 是否开启负风险
  show_market_images?: boolean; // 是否展示市场图片
  automatically_active?: boolean; // 是否自动激活

  liquidity?: number; // 流动性
  volume?: number; // 交易量，服务器算的，不应该让用户手写
  volume_24hr?: number; // 24 小时交易量
  competitive?: number; // 竞争力
  comment_count?: number; // 评论数量
  open_interest?: number; // 未平仓合约
  liquidity_clob?: number; // 流动性
  neg_risk_fee_bips?: number; // 负风险费用
};

// 自动生成项
type SystemGeneratedFields = {
  updated_by: string; // TODO：更新人，基于登录人本身钱包获取，不用用户手填（必传）

  id: number; // ID,服务器自动生成（不传）
  neg_risk_market_id: string; // event 上链之后，自动生成的值（不传）。TODO:如果有就已开盘，隐藏发布按钮
  created_at: string; // 创建时间，服务器自动生成（不传）
  updated_at: string; // 更新时间，服务器自动生成（不传）
};

// 最终的大类型
export type EventDataType = RequiredFields & OptionalFields & SystemGeneratedFields;
