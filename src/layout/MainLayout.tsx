import { Layout } from 'antd';
import ContainerHeader from './components/ContainerHeader';
import ContainerSider from './components/ContainerSider';
import ContainerMenu from './components/ContainerMenu';
import ContainerContent from './components/ContainerContent';
import ContainerFooter from './components/ContainerFooter';

const MainLayout = () => {
  return (
    <Layout className="min-h-full" style={{ height: '100vh', overflow: 'hidden' }}>
      <ContainerHeader />
      <Layout style={{ height: 'calc(100vh - 64px)' }}>
        <ContainerSider>
          <ContainerMenu collapsed={false} />
        </ContainerSider>
        <Layout style={{ display: 'flex', flexDirection: 'column' }}>
          <div style={{ flex: 1, overflowY: 'auto' }}>
            <ContainerContent />
          </div>
          <ContainerFooter />
        </Layout>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
