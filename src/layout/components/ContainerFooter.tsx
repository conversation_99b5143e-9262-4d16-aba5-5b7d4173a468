import { Layout, FloatButton, Typography, Divider } from 'antd';

const ContainerFooter = () => {
  return (
    <Layout.Footer className="text-center p-2">
      <Typography.Text type="secondary" className="text-xs">
        <div>
          <a href="https://test.predict.one/" target="_blank" rel="noopener noreferrer">
            PredictOne
          </a>
          <Divider type="vertical" style={{ margin: '0 4px' }} />
          <a href="https://hub.predict.one/" target="_blank" rel="noopener noreferrer">
            JOLTIFY Points Hub
          </a>
        </div>
      </Typography.Text>
      <FloatButton.BackTop />
    </Layout.Footer>
  );
};

export default ContainerFooter;
