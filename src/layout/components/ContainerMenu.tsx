import { useState, useEffect } from 'react';
import {
  BookOutlined,
  CameraOutlined,
  ShopOutlined,
  TrophyOutlined,
  PicLeftOutlined,
  TagsOutlined,
  PictureOutlined,
} from '@ant-design/icons';
import { Menu, type MenuProps } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { getCookie } from '@/utils';

const ContainerMenu = (props: { collapsed: boolean }) => {
  const { collapsed } = props;
  const navigator = useNavigate();
  const { pathname } = useLocation();

  const [role, setRole] = useState<string | undefined>(getCookie('jwt-role'));

  useEffect(() => {
    const observer = new MutationObserver(() => {
      const updatedRole = getCookie('jwt-role');
      if (updatedRole !== role) {
        setRole(updatedRole);
      }
    });

    observer.observe(document, {
      subtree: true,
      childList: true,
      attributes: true,
    });

    return () => observer.disconnect();
  }, [role]);

  const allMenuItems: MenuProps['items'] = [
    {
      key: '/',
      icon: (
        <div
          style={{
            backgroundColor: !collapsed && pathname === '/' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <BookOutlined
            style={{ color: !collapsed && pathname === '/' ? 'white' : 'black', fontSize: '18px' }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Guide</span>,
    },
    {
      key: '/banner',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/banner' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <PictureOutlined
            style={{
              color: !collapsed && pathname === '/banner' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Banner</span>,
    },
    {
      key: '/subheaders',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/subheaders' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <PicLeftOutlined
            style={{
              color: !collapsed && pathname === '/subheaders' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>SubHeader</span>,
    },
    {
      key: '/tags',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/tags' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <TagsOutlined
            style={{
              color: !collapsed && pathname === '/tags' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Tag</span>,
    },
    {
      key: '/events',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/events' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <ShopOutlined
            style={{
              color: !collapsed && pathname === '/events' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Event</span>,
    },
    {
      key: '/sportevents',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/sportevents' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <TrophyOutlined
            style={{
              color: !collapsed && pathname === '/sportevents' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Sport</span>,
    },
    {
      key: '/publish-result',
      icon: (
        <div
          style={{
            backgroundColor:
              !collapsed && pathname === '/publish-result' ? 'rgb(24, 144, 255)' : 'transparent',
            padding: !collapsed ? '6px' : '0px',
            borderRadius: '6px',
          }}
        >
          <CameraOutlined
            style={{
              color: !collapsed && pathname === '/publish-result' ? 'white' : 'black',
              fontSize: '18px',
            }}
          />
        </div>
      ),
      label: <span style={{ fontSize: collapsed ? '12px' : '16px' }}>Publish</span>,
    },
  ];

  // 根据角色过滤菜单项
  const filteredMenuItems = allMenuItems.filter((item) => {
    if (!item) return false;
    switch (role) {
      case 'admin':
        return true; // admin 可以看到所有菜单项
      case 'publish':
        return item.key === '/'; // publish 只能看到 Guide
      case 'event_writer':
        return ['/', '/tags', '/events', '/questions'].includes(item.key as string); // event_writer 看到 Event/Tag/Sport/Guide
      default:
        return false; // 其他角色看不到任何菜单项
    }
  });

  return (
    <Menu
      mode="inline"
      selectedKeys={[pathname]}
      items={filteredMenuItems}
      onClick={({ key }) => {
        navigator(key);
      }}
      inlineCollapsed={collapsed}
    />
  );
};

export default ContainerMenu;
