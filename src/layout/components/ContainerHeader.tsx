import { Layout, Space, Dropdown, theme, Avatar } from 'antd';
import { UserOutlined, LogoutOutlined, DownOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { FIXED_HEADER, NAV_HEIGHT } from '../../styles/theme';
import { clearLocalStorage } from '@/utils';
import LogoContent from '@/components/LogoContent';

const ContainerHeader = () => {
  const { token } = theme.useToken();
  const navigate = useNavigate();
  const redirectTo = (path: string) => {
    navigate(path);
  };
  const items = [
    // {
    //   key: '1',
    //   label: <Space>个人中心</Space>,
    //   icon: <UserOutlined />,
    //   onClick: () => {
    //     redirectTo('/profile');
    //   },
    // },
    // {
    //   key: '2',
    //   label: <Space>个人设置</Space>,
    //   icon: <SmileOutlined />,
    //   onClick: () => {
    //     redirectTo('/setting');
    //   },
    // },
    {
      key: '3',
      label: <Space>log out</Space>,
      icon: <LogoutOutlined />,
      onClick: () => {
        redirectTo('/login');
        clearLocalStorage();
        document.cookie = 'jwt-token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'; // Clear jwt-token cookie
        document.cookie = 'jwt-role=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'; // Clear jwt-role cookie
        document.cookie = 'server-auth=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'; // Clear server-auth cookie
      },
    },
  ];

  return (
    <Layout.Header
      className="flex justify-between items-center px-4"
      style={{
        borderStyle: 'solid',
        borderWidth: '0 0 1px 0',
        borderColor: token.colorBorderSecondary,
        ...(FIXED_HEADER
          ? {
              height: NAV_HEIGHT,
              position: 'sticky',
              top: 0,
              zIndex: 1,
              width: '100%',
            }
          : {
              height: NAV_HEIGHT,
            }),
      }}
    >
      <LogoContent onClick={() => redirectTo('/')} />
      <Dropdown
        menu={{
          items,
        }}
        className="px-4"
      >
        <Space className="cursor-pointer">
          <Avatar icon={<UserOutlined />} />
          <DownOutlined />
        </Space>
      </Dropdown>
    </Layout.Header>
  );
};

export default ContainerHeader;
