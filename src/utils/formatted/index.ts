import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

export const formatDate = (dateString: string): string => {
  // 使用 dayjs UTC 来解析和格式化时间，确保显示的是 UTC 时间
  const date = dayjs.utc(dateString);
  return date.format('YYYY.M.D HH:mm');
};

export function formatRangePickerDates(dates: [dayjs.Dayjs, dayjs.Dayjs]) {
  const [startDate, endDate] = dates;

  return {
    start_date: startDate.utc().format('YYYY-MM-DDTHH:mm:ss.SSSSSS'),
    end_date: endDate.utc().format('YYYY-MM-DDTHH:mm:ss.SSSSSS'),
  };
}
