export function getCookie(name: string): string | undefined {
  const value = `; ${document.cookie}`;
  const parts = value.split(`; ${name}=`);
  if (parts.length === 2) return parts.pop()?.split(';').shift();
}

export function calculateImageUrl(clearValues: any, name: string) {
  if (Array.isArray(clearValues[name]) && clearValues[name][0]?.url) {
    // 如果已经是完整的 URL，直接返回
    return clearValues[name][0].url;
  }

  if (Array.isArray(clearValues[name]) && clearValues[name][0]?.response?.image_name) {
    // 如果是上传图片的情况，生成完整的图片 URL
    return `${import.meta.env.VITE_OSS_IMG_URL}/${clearValues[name][0].response.image_name}`;
  }

  // 如果没有上传图片，返回原始值或空字符串
  return clearValues[name] || '';
}
