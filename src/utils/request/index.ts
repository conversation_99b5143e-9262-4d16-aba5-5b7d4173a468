import { notification } from 'antd';
import axios, { AxiosRequestConfig } from 'axios';
import { message } from 'antd';

function handleSessionExpired() {
  notification.error({
    message: 'JWT Expired',
    description: 'Your JWT Token has expired. Please log in again.',
  });
}

async function request(config: AxiosRequestConfig, successMessage?: string) {
  try {
    const response = await axios(config);
    if (successMessage) {
      message.success(successMessage);
    }
    return response;
  } catch (error: any) {
    if (error.response?.data?.code === 'invalid-jwt') {
      handleSessionExpired();
    }
    throw error;
  }
}
export { request, handleSessionExpired };
