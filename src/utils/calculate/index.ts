import { GLOBAL_LANGUAGES } from '@/utils';
import dayjs from 'dayjs';
interface Item {
  id: number;
  [key: string]: any;
}

export const uniqueById = <T extends Item>(array: T[]): T[] => {
  const seen = new Set<number>();
  return array.filter((item) => {
    const duplicate = seen.has(item.id);
    seen.add(item.id);
    return !duplicate;
  });
};
interface TransformedContent {
  language: string;
  content: string;
}

export const decodeFormData = (data: any) => {
  const languages = GLOBAL_LANGUAGES;
  const result = {
    slug: [] as TransformedContent[],
    title: [] as TransformedContent[],
    description: [] as TransformedContent[],
    multimedia_url: [] as TransformedContent[],
  };

  languages.forEach((lang) => {
    if (data[`title_${lang}`]) {
      result.title.push({ language: lang, content: data[`title_${lang}`] });
      result.slug.push({ language: lang, content: `${data.slug}_${Date.now()}` });
    }
    if (data[`description_${lang}`]) {
      result.description.push({ language: lang, content: data[`description_${lang}`] });
    }
    if (data[`multimedia_url_${lang}`]) {
      result.multimedia_url.push({ language: lang, content: data[`multimedia_url_${lang}`] });
    }
  });

  // Check whether it contains a 'en' language slug, and if not, add it
  if (!result.slug.some((item) => item.language === 'en')) {
    result.slug.push({ language: 'en', content: `${data.slug}_${dayjs.utc().valueOf()}` });
  }

  return encode(result);
};

export const processTitles = (data: any) => {
  const languages = GLOBAL_LANGUAGES;
  let resultTitle = '';

  languages.forEach((lang) => {
    if (data[`title_${lang}`]) {
      if (resultTitle) {
        resultTitle += ' ';
      }
      resultTitle += data[`title_${lang}`];
    }
  });

  return resultTitle;
};

export const encodeContent = (contentArray: TransformedContent[]) => {
  try {
    const jsonString = JSON.stringify(contentArray);
    // 使用 TextEncoder 将字符串转换为 Uint8Array
    const utf8Bytes = new TextEncoder().encode(jsonString);
    // 将 Uint8Array 转换为 Base64 字符串
    const base64String = btoa(String.fromCharCode(...utf8Bytes));

    return base64String;
  } catch (error) {
    console.error('Error encoding contentArray:', error);
    throw error;
  }
};

const encode = (result: {
  slug: TransformedContent[];
  title: TransformedContent[];
  description: TransformedContent[];
  multimedia_url: TransformedContent[];
}) => {
  try {
    const encodedSlug = encodeContent(result.slug);
    const encodedTitle = encodeContent(result.title);
    const encodedDescription = encodeContent(result.description);
    const encodedMediaUrl = encodeContent(result.multimedia_url);

    return {
      slug: encodedSlug,
      title: encodedTitle,
      description: encodedDescription,
      multimedia_url: encodedMediaUrl,
    };
  } catch (error) {
    console.error('Error encoding result:', error);
    throw error;
  }
};

export const clearFormData = (data: any) => {
  const fieldsToDelete = [
    'date_range',
    'tags',
    'languages', // 添加languages字段到删除列表
    'order_min_size',
    'order_price_min_tick_size_num',
    ...GLOBAL_LANGUAGES.map((lang) => `slug_${lang}`),
    ...GLOBAL_LANGUAGES.map((lang) => `title_${lang}`),
    ...GLOBAL_LANGUAGES.map((lang) => `description_${lang}`),
    ...GLOBAL_LANGUAGES.map((lang) => `multimedia_url_${lang}`),
  ];

  fieldsToDelete.forEach((field) => {
    if (data.hasOwnProperty(field)) {
      delete data[field];
    }
  });

  return data;
};

export const decode = (encodedContent: string): TransformedContent[] => {
  if (!encodedContent) {
    return [{ language: 'en', content: '' }];
  }

  try {
    const decodedString = decodeURIComponent(escape(atob(encodedContent)));
    if (isValidJson(decodedString)) {
      const parsedContent = JSON.parse(decodedString);
      if (Array.isArray(parsedContent)) {
        return parsedContent;
      } else {
        return [];
      }
    } else {
      return [];
    }
  } catch (error) {
    return [];
  }
};

const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch (e) {
    return false;
  }
};
export const decodeEventData = (data: any[]) => {
  return data.map((event) => ({
    ...event,
    key: event.id,
    slug: Array.isArray(event.slug) ? event.slug : decode(event.slug),
    title: Array.isArray(event.title) ? event.title : decode(event.title),
    description: Array.isArray(event.description) ? event.description : decode(event.description),
    multimedia_url: Array.isArray(event.multimedia_url)
      ? event.multimedia_url
      : decode(event.multimedia_url),
  }));
};

export const decodeQuestionData = (data: any[]) => {
  return data.map(({ question_market }) => {
    const decodedSlug = Array.isArray(question_market.slug)
      ? question_market.slug
      : decode(question_market.slug);
    const decodedQuestion = Array.isArray(question_market.question)
      ? question_market.question
      : decode(question_market.question);
    const decodedDescription = Array.isArray(question_market.description)
      ? question_market.description
      : decode(question_market.description);

    return {
      ...question_market,
      key: question_market.id,
      slug: decodedSlug,
      question: decodedQuestion,
      description: decodedDescription,
    };
  });
};

export const encodeBase64 = (str: string) => {
  try {
    const utf8Bytes = new TextEncoder().encode(str);
    return btoa(String.fromCharCode(...utf8Bytes));
  } catch (error) {
    console.error('Error encoding string:', error);
    throw error;
  }
};

export const generateRandomNonce = (length = 16): string => {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let nonce = '';
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    nonce += characters[randomIndex];
  }
  return nonce;
};

/**
 * 清理 markdown 语法，将其转换为纯文本
 * @param text 包含 markdown 语法的文本
 * @returns 清理后的纯文本
 */
export const stripMarkdown = (text: string): string => {
  if (!text || typeof text !== 'string') {
    return '';
  }

  let cleanText = text;

  // 移除代码块 (```code```)
  cleanText = cleanText.replace(/```[\s\S]*?```/g, '');

  // 移除行内代码 (`code`)
  cleanText = cleanText.replace(/`([^`]+)`/g, '$1');

  // 移除标题 (# ## ### #### ##### ######)
  cleanText = cleanText.replace(/^#{1,6}\s+(.*)$/gm, '$1');

  // 移除粗体 (**text** 或 __text__)
  cleanText = cleanText.replace(/\*\*([^*]+)\*\*/g, '$1');
  cleanText = cleanText.replace(/__([^_]+)__/g, '$1');

  // 移除斜体 (*text* 或 _text_)
  cleanText = cleanText.replace(/\*([^*]+)\*/g, '$1');
  cleanText = cleanText.replace(/_([^_]+)_/g, '$1');

  // 移除删除线 (~~text~~)
  cleanText = cleanText.replace(/~~([^~]+)~~/g, '$1');

  // 移除链接 [text](url) 保留文本
  cleanText = cleanText.replace(/\[([^\]]+)\]\([^)]+\)/g, '$1');

  // 移除图片 ![alt](url)
  cleanText = cleanText.replace(/!\[([^\]]*)\]\([^)]+\)/g, '$1');

  // 移除引用 (> text)
  cleanText = cleanText.replace(/^>\s+(.*)$/gm, '$1');

  // 移除水平线 (--- 或 ***)
  cleanText = cleanText.replace(/^[-*]{3,}$/gm, '');

  // 移除列表标记 (- * + 1. 2. etc.)
  cleanText = cleanText.replace(/^[\s]*[-*+]\s+(.*)$/gm, '$1');
  cleanText = cleanText.replace(/^[\s]*\d+\.\s+(.*)$/gm, '$1');

  // 移除表格语法
  cleanText = cleanText.replace(/\|.*\|/g, '');
  cleanText = cleanText.replace(/^[\s]*\|?[\s]*:?-+:?[\s]*\|?[\s]*$/gm, '');

  // 移除 HTML 标签
  cleanText = cleanText.replace(/<[^>]*>/g, '');

  // 清理多余的空行，保留单个换行
  cleanText = cleanText.replace(/\n{3,}/g, '\n\n');

  // 清理行首行尾的空白字符
  cleanText = cleanText.replace(/^[\s]+|[\s]+$/gm, '');

  // 清理整体首尾空白
  cleanText = cleanText.trim();

  return cleanText;
};

export function parseRulesContent(encodedString: string): string {
  try {
    const decodedBytes = Uint8Array.from(atob(encodedString), (c) => c.charCodeAt(0));
    const decodedString = new TextDecoder('utf-8').decode(decodedBytes);
    return decodedString;
  } catch (error) {
    console.error('Failed to parse encoded string:', error);
    return '';
  }
}

export const generateUniqueSlug = (
  slugArray: { language: string; content: string }[],
  timestamp: number = dayjs.utc().valueOf()
): { language: string; content: string }[] => {
  // 找到 language 为 "en" 的 slug
  const enSlug = slugArray.find((item) => item.language === 'en')?.content;

  if (!enSlug) {
    throw new Error('No "en" slug found in the array');
  }

  // 提取时间戳前的部分
  const baseSlug = enSlug.split('_').slice(0, -1).join('_');

  // 替换所有语言的 slug
  return slugArray.map((item) => ({
    ...item,
    content: `${baseSlug}_${timestamp}`, // 拼接新的唯一 slug
  }));
};
