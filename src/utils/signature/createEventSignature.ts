import { ethers } from 'ethers';
import { SiweMessage } from 'siwe';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import dayjs from 'dayjs';
import { releaseEvent } from '@/api/events';
import { generateRandomNonce } from '../index';

const handleReleaseEvent = async (record: any, setLoading: any, message: any, fetchData: any) => {
  if (window.ethereum) {
    try {
      setLoading({ [record.id]: true });
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const nonce = generateRandomNonce(16);

      const siweMessage = new SiweMessage({
        address: address,
        chainId: Number(import.meta.env.VITE_CHAIN_ID),
        nonce: nonce,
        domain: import.meta.env.VITE_BACK_SIGNATURE_DOMAIN,
        uri: import.meta.env.VITE_BACK_SIGNATURE_URL,
        expirationTime: dayjs.utc().add(7, 'day').toISOString(),
        statement: 'Welcome to Prediction One! Sign to verify.',
        version: '1',
        resources: [`eventId:${record.id}`, `fee:${record.neg_risk_fee_bips || '0'}`],
      });

      const messageToSign = siweMessage.prepareMessage();
      const signature = await signer.signMessage(messageToSign);

      try {
        const siweMessage = new SiweMessage(messageToSign);
        const fields = await siweMessage.verify({ signature });

        if (fields.success) {
          const authHeader = `${JSON.stringify(siweMessage)}:::${signature}`;
          const authHeaderBase64 = Buffer.from(authHeader).toString('base64');

          await releaseEvent(authHeaderBase64);
          fetchData();
        }
      } catch (error) {
        console.error('Error validating SIWE message:', error);
        return null;
      }
    } catch (error) {
      message.error('Failed to login with MetaMask');
    } finally {
      setLoading({ [record.id]: false });
    }
  } else {
    message.error('MetaMask is not installed. Please install MetaMask and try again.');
  }
};

export default handleReleaseEvent;
