import { ethers } from 'ethers';
import { SiweMessage } from 'siwe';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import dayjs from 'dayjs';
import { releaseQuestion } from '@/api/questions';
import { generateRandomNonce } from '../index';

const handleReleaseQuestion = async (
  record: any,
  setLoading: any,
  message: any,
  fetchData: any,
  params: any
) => {
  if (window.ethereum) {
    try {
      setLoading({ [record.id]: true });
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const nonce = generateRandomNonce(16);

      const siweMessage = new SiweMessage({
        address: address,
        chainId: Number(import.meta.env.VITE_CHAIN_ID),
        nonce: nonce,
        domain: import.meta.env.VITE_BACK_SIGNATURE_DOMAIN,
        uri: import.meta.env.VITE_BACK_SIGNATURE_URL,
        expirationTime: dayjs.utc().add(7, 'day').toISOString(),
        statement: 'Welcome to Prediction One! Sign to verify.',
        version: '1',
        resources: [
          `eventId:${params.eventId}`,
          `question:${params.question}`,
          `questionIndexId:${record.id}`,
        ],
      });

      const messageToSign = siweMessage.prepareMessage();
      const signature = await signer.signMessage(messageToSign);

      try {
        const siweMessage = new SiweMessage(messageToSign);
        const fields = await siweMessage.verify({ signature });
        if (fields.success) {
          const authHeader = `${JSON.stringify(siweMessage)}:::${signature}`;
          const authHeaderBase64 = Buffer.from(authHeader).toString('base64');

          const response = await releaseQuestion(authHeaderBase64);
          if (Object.keys(response.data || {}).length > 0) {
            fetchData();
            message.success('Question published successfully');
          } else {
            message.error('Failed to publish question. Please try again.');
          }
        }
      } catch (error) {
        message.error(`${(error as any).response?.data?.error?.error || 'An error occurred'}`);
        return null;
      }
    } catch (error) {
      message.error('User cancels signature');
    } finally {
      setLoading({ [record.id]: false });
    }
  } else {
    message.error('MetaMask is not installed. Please install MetaMask and try again.');
  }
};

export default handleReleaseQuestion;
