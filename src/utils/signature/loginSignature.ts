import Cookies from 'js-cookie';
import { fetchUserRole, getJWTToken } from '@/api/login';
import { ethers } from 'ethers';
import dayjs from 'dayjs';
import { setLocalStorage } from '@/utils';
import { createClobAuth } from '@/api/clob/createClobAuth';

const createEIP712Payload = (address: string, timestamp: string) => {
  const domain = {
    name: 'ClobAuthDomain',
    version: '1',
    chainId: Number(import.meta.env.VITE_CHAIN_ID),
    verifyingContract: '******************************************',
  };

  const types = {
    EIP712Domain: [
      { name: 'name', type: 'string' },
      { name: 'version', type: 'string' },
      { name: 'chainId', type: 'uint256' },
      { name: 'verifyingContract', type: 'address' },
    ],
    ClobAuth: [
      { name: 'address', type: 'address' },
      { name: 'timestamp', type: 'string' },
      { name: 'nonce', type: 'uint256' },
      { name: 'message', type: 'string' },
    ],
  };

  const message = {
    address: address.toLowerCase(),
    timestamp,
    nonce: 0,
    message: 'This message attests that I control the given wallet',
  };

  return { domain, types, primaryType: 'ClobAuth', message };
};

const requestEthereumAccounts = async () => {
  if (typeof window.ethereum === 'undefined') {
    throw new Error('Ethereum provider is not available. Please install MetaMask.');
  }

  await window.ethereum.request({ method: 'eth_requestAccounts' });
  const provider = new ethers.BrowserProvider(window.ethereum);
  const signer = await provider.getSigner();
  const address = await signer.getAddress();
  return { signer, address };
};

const getCurrentTimestamp = (): number => {
  return Math.floor(dayjs.utc().valueOf() / 1000);
};

const setCookies = (role: string, jwtToken: string, setCookie?: string) => {
  Cookies.set('jwt-role', role, { expires: 1 });
  Cookies.set('jwt-token', jwtToken, { expires: 1 });
  if (setCookie) {
    Cookies.set('server-auth', setCookie, { expires: 1 });
  }
};

export const handleLoginResponse = async (signer: any, address: string, creds: any) => {
  try {
    const response = await fetchUserRole(address);
    const { role, setCookie } = response;
    const jwtToken = await getJWTToken(signer, creds, role, getCurrentTimestamp());

    if (role) {
      setCookies(role, jwtToken, setCookie);
      return { success: true, role };
    }
    return { success: false, error: 'No role assigned' };
  } catch (error: any) {
    console.error('Login failed:', error);
    return {
      success: false,
      error: error.message || 'Login failed. Please contact administrator for access.',
    };
  }
};

const handleSignatureVerification = async (address: string, payload: any, timestamp: string) => {
  const params = [address.toLowerCase(), JSON.stringify(payload)];
  if (!window.ethereum) {
    throw new Error('Ethereum provider is not available. Please install MetaMask.');
  }
  const signature = await window.ethereum.request({
    method: 'eth_signTypedData_v4',
    params,
  });

  return await createClobAuth({
    address,
    signature,
    timestamp,
  });
};

const verifySignatureAndLogin = async (
  signer: any,
  address: string,
  payload: any,
  timestamp: string,
  message: any,
  navigate: any
) => {
  try {
    const response = await handleSignatureVerification(address, payload, timestamp);
    if (response) {
      const loginResult = await handleLoginResponse(signer, address, response);
      if (loginResult.success) {
        message.success(`Logged in with MetaMask as ${loginResult.role}`);
        navigate('/');
      } else {
        message.error(loginResult.error || 'Login failed');
      }
    } else {
      message.error('Signature verification failed');
    }
  } catch (error: any) {
    console.error('Login error:', error);
    message.error(error.message || 'Login failed. Please try again.');
  }
};

export const handleLogin = async (
  setLoading: (loading: boolean) => void,
  message: any,
  navigate: any
) => {
  if (!window.ethereum) {
    message.error('MetaMask is not installed. Please install MetaMask and try again.');
    return;
  }

  try {
    setLoading(true);
    const { signer, address } = await requestEthereumAccounts();
    setLocalStorage('userWallet', address);

    const timestamp = getCurrentTimestamp().toString();
    const payload = createEIP712Payload(address, timestamp);

    await verifySignatureAndLogin(signer, address, payload, timestamp, message, navigate);
  } catch (error) {
    console.error('Error during login:', error);
    message.error('Failed to login with MetaMask');
  } finally {
    setLoading(false);
  }
};
