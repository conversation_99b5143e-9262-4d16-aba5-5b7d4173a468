import { ethers } from 'ethers';
import { SiweMessage } from 'siwe';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import dayjs from 'dayjs';
import { releaseResult } from '@/api/questions';
import { generateRandomNonce } from '../index';
import { message } from 'antd';
const handlePublishResult = async (record: any, outcome: string) => {
  if (window.ethereum) {
    try {
      await window.ethereum.request({ method: 'eth_requestAccounts' });
      const provider = new ethers.BrowserProvider(window.ethereum);
      const signer = await provider.getSigner();
      const address = await signer.getAddress();
      const nonce = generateRandomNonce(16);

      const siweMessage = new SiweMessage({
        address: address,
        chainId: Number(import.meta.env.VITE_CHAIN_ID),
        nonce: nonce,
        domain: import.meta.env.VITE_BACK_SIGNATURE_DOMAIN,
        uri: import.meta.env.VITE_BACK_SIGNATURE_URL,
        expirationTime: dayjs.utc().add(7, 'day').toISOString(),
        statement: 'Welcome to Prediction One! Sign to verify.',
        version: '1',
        resources: [`questionIndexId:${record.id}`, `outcome:${outcome}`],
      });

      const messageToSign = siweMessage.prepareMessage();
      const signature = await signer.signMessage(messageToSign);

      try {
        const siweMessage = new SiweMessage(messageToSign);
        const fields = await siweMessage.verify({ signature });

        if (fields.success) {
          const authHeader = `${JSON.stringify(siweMessage)}:::${signature}`;
          const authHeaderBase64 = Buffer.from(authHeader).toString('base64');
          const res = await releaseResult(authHeaderBase64);
          message.success('Result published successfully!');
          return res.data;
        }
      } catch (error: any) {
        if (error.response) {
          const errorMessage =
            error.response.data || error.response.data?.error || 'An error occurred on the server.';

          // 检查是否是 "invalid request" 错误
          if (
            typeof errorMessage === 'string' &&
            errorMessage.toLowerCase().includes('invalid request')
          ) {
            message.error('No event results yet');
          } else {
            message.error(`Server Error: ${errorMessage}`);
          }
        } else if (error.request) {
          message.error('No response received from the server. Please try again later.');
        } else {
          message.error(`Unexpected Error: ${error.message}`);
        }

        return null;
      }
    } catch (error) {
      message.error('User cancels signature');
    }
  } else {
    message.error('MetaMask is not installed. Please install MetaMask and try again.');
  }
};

export default handlePublishResult;
