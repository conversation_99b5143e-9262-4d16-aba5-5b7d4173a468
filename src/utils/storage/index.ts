export const getLocalStorage = (key: any) => {
  const value = window.localStorage.getItem(key);
  try {
    return value ? JSON.parse(value) : null;
  } catch (error) {
    return value;
  }
};

export const setLocalStorage = (key: string, value: any) => {
  window.localStorage.setItem(key, JSON.stringify(value));
};

export const removeLocalStorage = (key: string) => {
  window.localStorage.removeItem(key);
};

export const clearLocalStorage = () => {
  window.localStorage.clear();
};
