import CryptoJS from 'crypto-js';

// 使用应用特定的密钥，结合用户信息增加安全性
const getSecretKey = (userWallet?: string): string => {
  const baseKey = import.meta.env.VITE_APP_SECRET_KEY || 'pdone-backstage-secret-2024';
  return userWallet ? `${baseKey}-${userWallet.slice(-8)}` : baseKey;
};

// 生成用户特定的存储键名
const getStorageKey = (userWallet?: string): string => {
  if (!userWallet) {
    return 'secure_event_limit';
  }
  // 使用钱包地址的哈希作为键名的一部分，确保每个用户有独立的存储空间
  const hash = CryptoJS.SHA256(userWallet.toLowerCase()).toString().substring(0, 8);
  return `secure_event_limit_${hash}`;
};

// 加密存储事件限制
export function setSecureEventLimit(eventLimit: number, userWallet?: string): void {
  try {
    const secretKey = getSecretKey(userWallet);
    const encrypted = CryptoJS.AES.encrypt(eventLimit.toString(), secretKey).toString();
    localStorage.setItem(getStorageKey(userWallet), encrypted);
  } catch (error) {
    console.error('Failed to store event limit securely:', error);
  }
}

// 解密获取事件限制
export function getSecureEventLimit(userWallet?: string): number {
  try {
    const encrypted = localStorage.getItem(getStorageKey(userWallet));

    if (!encrypted) {
      return parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);
    }

    const secretKey = getSecretKey(userWallet);

    // 解密事件限制
    const decrypted = CryptoJS.AES.decrypt(encrypted, secretKey).toString(CryptoJS.enc.Utf8);
    const eventLimit = parseInt(decrypted, 10);

    return isNaN(eventLimit)
      ? parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10)
      : eventLimit;
  } catch (error) {
    console.error('Failed to get event limit securely:', error);
    // 出错时清除可能损坏的数据
    clearSecureEventLimit(userWallet);
    return parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);
  }
}

// 清除安全存储
export function clearSecureEventLimit(userWallet?: string): void {
  try {
    localStorage.removeItem(getStorageKey(userWallet));
  } catch (error) {
    console.error('Failed to clear secure event limit:', error);
  }
}

// 检查存储的事件限制是否有效
export function isSecureEventLimitValid(userWallet?: string): boolean {
  try {
    const encrypted = localStorage.getItem(getStorageKey(userWallet));
    return !!encrypted;
  } catch (error) {
    return false;
  }
}
