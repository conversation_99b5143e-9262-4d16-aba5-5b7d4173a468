import { getCookie } from '@/utils';
import { message } from 'antd';

/**
 * 解析JWT token获取过期时间
 * @param token JWT token
 * @returns 过期时间戳或null
 */
function parseJWTExpiration(token: string): number | null {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = JSON.parse(atob(parts[1]));
    return payload.exp ? payload.exp * 1000 : null; // 转换为毫秒
  } catch (error) {
    console.error('Failed to parse JWT token:', error);
    return null;
  }
}

/**
 * 检查JWT token是否存在且未过期
 * @returns { isValid: boolean, message?: string }
 */
export function validateJWT(): { isValid: boolean; message?: string } {
  const jwtToken = getCookie('jwt-token');
  const userRole = getCookie('jwt-role');

  // 检查JWT token是否存在
  if (!jwtToken) {
    return {
      isValid: false,
      message: 'Authentication required. Please log in to continue.',
    };
  }

  // 检查用户角色是否存在
  if (!userRole) {
    return {
      isValid: false,
      message: 'User role not found. Please log in again.',
    };
  }

  // 检查JWT token是否过期
  const expirationTime = parseJWTExpiration(jwtToken);
  if (expirationTime && Date.now() >= expirationTime) {
    return {
      isValid: false,
      message: 'Your session has expired. Please log in again.',
    };
  }

  return { isValid: true };
}

/**
 * 验证JWT并显示错误提示，如果验证失败则阻止操作
 * @param actionName 操作名称，用于错误提示
 * @returns boolean 是否验证通过
 */
export function validateJWTWithMessage(actionName: string = 'perform this action'): boolean {
  const validation = validateJWT();

  if (!validation.isValid) {
    message.error({
      content: `🔒 ${validation.message} Unable to ${actionName}.`,
      duration: 5,
      style: {
        marginTop: '10px',
      },
    });
    return false;
  }

  return true;
}

/**
 * 检查用户是否有权限执行特定操作
 * @param requiredRoles 允许的角色列表
 * @param actionName 操作名称
 * @returns boolean 是否有权限
 */
export function checkUserPermission(
  requiredRoles: string[],
  actionName: string = 'perform this action'
): boolean {
  // 首先验证JWT
  if (!validateJWTWithMessage(actionName)) {
    return false;
  }

  const userRole = getCookie('jwt-role');

  if (!requiredRoles.includes(userRole!)) {
    message.error({
      content: `🚫 You don't have permission to ${actionName}. Required roles: ${requiredRoles.join(', ')}`,
      duration: 5,
      style: {
        marginTop: '10px',
      },
    });
    return false;
  }

  return true;
}
