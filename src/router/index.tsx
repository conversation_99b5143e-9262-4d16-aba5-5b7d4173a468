import React, { Suspense, lazy } from 'react';
import Loading from '@/components/Loading';

const lazyLoad = (Component: React.LazyExoticComponent<React.FC>) => (
  <Suspense fallback={<Loading />}>
    <Component />
  </Suspense>
);

const MainLayout = lazy(() => import('@/layout/MainLayout'));
const AuthLayout = lazy(() => import('@/layout/AuthLayout'));
const Login = lazy(() => import('@/pages/Login'));
const Register = lazy(() => import('@/pages/Register'));
const Home = lazy(() => import('@/pages/Home'));
const NoPermission = lazy(() => import('@/pages/ErrorPage/403'));
const NoMatch = lazy(() => import('@/pages/ErrorPage/404'));
const User = lazy(() => import('@/pages/User'));
const ManageEvents = lazy(() => import('@/pages/ManageEvents'));
const ManageQuestions = lazy(() => import('@/pages/ManageQuestions'));
const ManageTags = lazy(() => import('@/pages/ManageTags'));
const PublishResult = lazy(() => import('@/pages/PublishResult'));
const ManageSubHeader = lazy(() => import('@/pages/ManageSubHeader'));
const ManageSportEvent = lazy(() => import('@/pages/ManageSportEvents'));
const ManageBanner = lazy(() => import('@/pages/ManageBanner'));

const routes = [
  {
    path: '/',
    name: 'Home',
    key: '/',
    auth: true,
    element: lazyLoad(MainLayout),
    children: [
      {
        index: true,
        name: 'Home',
        key: '/',
        auth: true,
        element: lazyLoad(Home),
      },
      {
        index: false,
        path: 'banner',
        name: 'banner',
        key: '/banner',
        auth: true,
        element: lazyLoad(ManageBanner),
      },
      {
        index: false,
        path: 'events',
        name: 'events',
        key: '/events',
        auth: true,
        element: lazyLoad(ManageEvents),
      },
      {
        index: false,
        path: 'questions',
        name: 'questions',
        key: '/questions',
        auth: true,
        element: lazyLoad(ManageQuestions),
      },
      {
        index: false,
        path: 'tags',
        name: 'tags',
        key: '/tags',
        auth: true,
        element: lazyLoad(ManageTags),
      },
      {
        index: false,
        path: 'subheaders',
        name: 'subheaders',
        key: '/subheaders',
        auth: true,
        element: lazyLoad(ManageSubHeader),
      },
      {
        index: false,
        path: 'sportevents',
        name: 'sportevents',
        key: '/sportevents',
        auth: true,
        element: lazyLoad(ManageSportEvent),
      },
      {
        index: false,
        path: 'publish-result',
        name: 'publish-result',
        key: '/publish-result',
        auth: true,
        element: lazyLoad(PublishResult),
      },

      {
        index: false,
        path: 'user',
        name: 'User',
        key: '/user',
        auth: true,
        element: lazyLoad(User),
      },

      {
        path: '*',
        name: 'No Match',
        key: '*',
        element: lazyLoad(NoMatch),
      },
    ],
  },
  {
    index: false,
    path: 'login',
    name: '登录',
    key: '/login',
    auth: false,
    element: lazyLoad(AuthLayout),
    children: [
      {
        index: true,
        name: '登录',
        key: '/login',
        auth: false,
        element: lazyLoad(Login),
      },
    ],
  },
  {
    index: false,
    path: 'register',
    name: '注册',
    key: '/register',
    auth: false,
    element: lazyLoad(AuthLayout),
    children: [
      {
        index: true,
        name: '注册',
        key: '/register',
        auth: false,
        element: lazyLoad(Register),
      },
    ],
  },
  {
    index: false,
    path: '/403',
    name: '403',
    key: '/403',
    auth: false,
    element: lazyLoad(NoPermission),
  },
  {
    path: '*',
    name: 'No Match',
    key: '*',
    element: lazyLoad(NoMatch),
  },
];

export default routes;
