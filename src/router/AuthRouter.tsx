import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import Cookies from 'js-cookie';

const AuthRouter = (props: { children: React.ReactNode }) => {
  const location = useLocation(); // 获取当前路由对象

  // 检查后端设置的 server-auth Cookie
  const serverAuth = Cookies.get('server-auth');

  // 如果当前不是登录页，且没有 server-auth，跳转到登录页
  if (!serverAuth && location.pathname !== '/login') return <Navigate to="/login" replace />;

  return props.children;
};

export default AuthRouter;
