import { request } from '@/utils';
import { getCookie } from '@/utils';

type Params = {
  label: string;
  slug: string;
  region: string;
  sync?: boolean;
  force_hide?: boolean;
  force_show?: boolean;
};

export async function fetchTagsList() {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetTagsList?limit=100&offset=0`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export async function fetchTagsListByRegion(region: string[]) {
  const sortedRegion = region.sort();

  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetTagsListByRegion`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      limit: 100,
      offset: 0,
      region: sortedRegion.map((r) => `%${r}%`).join(','),
    },
  });
}

export async function fetchTagsListBySearch(label: string) {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetTagsListBySearch`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      label: `%${label}%`, // 通配符模糊匹配
    },
  });
}

export async function fetchAddTag(params: Params) {
  const jwtToken = getCookie('jwt-token');
  const sortedRegion = params.region.split(',').sort().join(',');

  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddTag`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: {
      label: params.label,
      slug: params.slug,
      sync: params.sync,
      force_hide: params.force_hide,
      force_show: params.force_show,
      region: sortedRegion,
    },
  });
}
