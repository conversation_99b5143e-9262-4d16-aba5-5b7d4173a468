import axios from 'axios';
import { getUserRoleByWallet } from '@/constants/whitelist';

const getJWTToken = async function (signer: any, creds: any, role: string, timestamp?: number) {
  try {
    const tokenResponse = await axios({
      url: `${import.meta.env.VITE_SERVER_URL}/get-jwt-token`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        signer,
        creds,
        role,
        timestamp,
      },
    });

    return tokenResponse.data.jwt;
  } catch (error) {
    console.error('Error in getJWTToken:', error);
    throw error;
  }
};

const fetchUserRole = async function (user_id: string) {
  try {
    const role = getUserRoleByWallet(user_id);

    // 手动设置 Cookie
    const cookieValue = `user_id=${user_id}; Path=/; HttpOnly`;
    document.cookie = cookieValue;

    return {
      role,
      setCookie: cookieValue,
    };
  } catch (error) {
    console.error('Error in fetchUserRole:', error);
    throw error;
  }
};

export { fetchUserRole, getJWTToken };
