import { request, getCookie } from '@/utils';

export async function fetchQuestionList(eventId: number, address: string) {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetQuestionList?tid=${eventId}&address=${address}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function fetchAddQuestion(questionParams: any) {
  const jwtToken = getCookie('jwt-token');

  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddQuestion`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${jwtToken}` },
    data: { question_market: questionParams },
  });

  // 根据实际响应结构返回数据
  if (response.data?.insert_question_market?.returning?.[0]) {
    return response.data.insert_question_market.returning[0];
  } else {
    console.error('Unexpected response structure:', response.data);
    throw new Error('Question creation API returned unexpected response structure');
  }
}

export async function fetchUpdateQuestion(id: number, questionParams: any) {
  const jwtToken = getCookie('jwt-token');

  await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backUpdateQuestion`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${jwtToken}` },
    data: { id: id, question: questionParams },
  });
}

export async function fetchDeleteQuestion(Id: number) {
  const jwtToken = getCookie('jwt-token');

  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backDeleteQuestion`,
    method: 'delete',
    headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${jwtToken}` },
    data: { Id: Id },
  });
}

export async function fetchLinkEventQuestion(event_question: {
  event_id: number;
  condition_id: string;
}) {
  const jwtToken = getCookie('jwt-token');

  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backLinkEventQuestion`,
    method: 'post',
    headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${jwtToken}` },
    data: { event_question: event_question },
  });

  return response.data;
}

export async function releaseQuestion(authHeaderBase64: string) {
  return await request({
    url: `${import.meta.env.VITE_PDONE_ENDPOINT}/add-question`,
    method: 'get',
    headers: { 'Content-Type': 'application/json', Authorization: `${authHeaderBase64}` },
  });
}

export async function releaseResult(authHeaderBase64: string) {
  return await request({
    url: `${import.meta.env.VITE_PDONE_ENDPOINT}/publish-result`,
    method: 'get',
    headers: { 'Content-Type': 'application/json', Authorization: `${authHeaderBase64}` },
  });
}

export async function fetchEventsById(eventId: Number) {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/getDetailById?tid=${eventId}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });
}
