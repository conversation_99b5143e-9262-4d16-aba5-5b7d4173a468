import { request } from '@/utils';
import { getCookie } from '@/utils';

export async function fetchSubHeaderList() {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/getSubHeaderList`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

export async function fetchAddSubHeader(SubHeaderParams: any) {
  const jwtToken = getCookie('jwt-token');

  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddSubHeader`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { suber_header: SubHeaderParams },
  });

  return response.data.insert_events.returning[0];
}

export async function fetchEditSubHeader(id: number, subHeaderParams: any) {
  const jwtToken = getCookie('jwt-token');

  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backUpdateSubHeader`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { id: id, suber_header: subHeaderParams },
  });

  return response.data.insert_events.returning[0];
}

export async function fetchDeleteSubHeader(id: number) {
  const jwtToken = getCookie('jwt-token');

  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backDeleteSubHeader`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: {
      id,
    },
  });
}
