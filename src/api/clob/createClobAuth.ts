import axios from 'axios';

const createClobAuth = async ({
  address,
  signature,
  timestamp,
}: {
  address: string;
  signature: string;
  timestamp: string;
}) => {
  try {
    const response = await axios({
      url: `${import.meta.env.VITE_PDONE_ENDPOINT}/auth/derive-api-key`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        'Pedone-address': address,
        'Pedone-signature': signature,
        'Pedone-timestamp': timestamp,
        'Pedone-nonce': '0',
      },
    });

    return response.data;
  } catch (error) {
    console.error('Error in createClobAuth:', error);
    throw error;
  }
};

export { createClobAuth };
