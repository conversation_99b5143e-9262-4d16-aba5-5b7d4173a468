import { request, getCookie, getLocalStorage } from '@/utils';
import { EventParams } from './types';

export async function fetchEventsList(
  userWallet: String,
  limit: number = 20,
  offset: number = 0,
  filter?: string
) {
  let whereCondition = {};

  // 根据filter参数构建where条件
  if (filter === 'active') {
    whereCondition = {
      closed: { _eq: false },
    };
  } else if (filter === 'closed') {
    whereCondition = {
      closed: { _eq: true },
    };
  }
  // filter为undefined时（All选项），whereCondition为空对象，不进行筛选

  const url = `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventsList`;

  // 使用 POST 请求传递复杂参数
  return request({
    url,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      address: userWallet,
      limit: limit,
      offset: offset,
      where: whereCondition,
    },
  });
}

export async function fetchEventsListBySearch(title: string) {
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventsListBySearch`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { title },
  });
}

export async function fetchEventsListByTag(userWallet: String, tagList: number[]) {
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventsListByTag`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: { address: userWallet, tagIds: tagList },
  });
}

// 获取用户事件创建限制数量（从数据库或环境变量）
export async function fetchUserEventLimit(userWallet: string): Promise<number> {
  try {
    const result = await request({
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetUserEventLimit`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
      },
      params: { user_wallet: userWallet },
    });

    // 如果找到用户配置，返回配置的数量；否则返回默认值
    const userConfig = result.data.event_allowed[0];
    const eventLimit = userConfig
      ? userConfig.event_num
      : parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);

    return eventLimit;
  } catch (error) {
    console.error('Failed to fetch user event limit:', error);
    // 出错时返回环境变量默认值
    return parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);
  }
}

// 检查用户事件创建数量的函数
async function checkUserEventLimit(userWallet: string): Promise<void> {
  try {
    // 获取用户的事件限制数量（从加密存储或服务器）
    const maxEventsPerUser = await fetchUserEventLimit(userWallet);

    const result = await request({
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventsList`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        address: userWallet,
        limit: 1000, // 获取所有事件来计数
        offset: 0,
        where: {},
      },
    });

    const userEvents = result.data.events.filter((event: any) => event.updated_by === userWallet);

    if (userEvents.length >= maxEventsPerUser) {
      throw new Error(
        `You have reached the maximum limit of ${maxEventsPerUser} events. Please contact an administrator if you need to create more events.`
      );
    }
  } catch (error: any) {
    if (error.message.includes('maximum limit')) {
      throw error;
    }
    // 如果检查失败，记录错误但不阻止创建（避免因为网络问题阻止正常用户）
    console.warn('Failed to check event limit:', error);
  }
}

export async function fetchAddEvent(eventParams: EventParams) {
  const jwtToken = getCookie('jwt-token');

  // 检查用户角色和事件数量限制
  const userRole = getCookie('jwt-role');
  if (userRole === 'event_writer') {
    const userWallet = getLocalStorage('userWallet');
    if (userWallet) {
      await checkUserEventLimit(userWallet);
    }
  }

  try {
    return await request({
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddEvent`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken}`,
      },
      data: { event: eventParams },
    }).then((response) => response.data.insert_events.returning[0]);
  } catch (error: any) {
    // 检查是否是权限错误（达到创建限制）
    if (
      error.response?.data?.errors?.[0]?.message?.includes('check constraint') ||
      error.response?.data?.errors?.[0]?.extensions?.code === 'permission-error'
    ) {
      // 获取用户的实际事件限制
      const userWallet = getLocalStorage('userWallet');
      const maxEventsPerUser = userWallet
        ? await fetchUserEventLimit(userWallet)
        : parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);

      throw new Error(
        `You have reached the maximum limit of ${maxEventsPerUser} events. Please contact an administrator if you need to create more events.`
      );
    }
    throw error;
  }
}

export async function fetchAddSearchEvent(eventId: string | number, fullTitle: string) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddSearchEvent`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { eventId, fullTitle },
  }).then((response) => response.data.insert_events.returning[0]);
}

export async function fetchUpdateEvent(eventId: number, eventParams: EventParams) {
  const jwtToken = getCookie('jwt-token');
  // 如果 eventParams 中包含 id，使用它；否则使用传入的 eventId
  const actualId = (eventParams as any).id || eventId;
  // 确保不将 id 传递到 event 对象中
  const { id, ...cleanEventParams } = eventParams as any;

  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backUpdateEvent`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { id: actualId, event: cleanEventParams },
  }).then((response) => response.data.update_events.returning[0]);
}

export async function fetchDeleteEvent(eventId: number) {
  const jwtToken = getCookie('jwt-token');
  return request(
    {
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backDeleteEvent`,
      method: 'delete',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${jwtToken}`,
      },
      data: { eventId },
    },
    'Event deleted successfully'
  );
}

export async function fetchAddEventTags(event_tags: { event_id: number; tag_id: number }) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddEventTags`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { event_tags },
  });
}

export async function fetchGetEventTags(event_id: number) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventTagsId?event_id=${event_id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
  });
}

// 获取用户创建的事件数量
export async function fetchUserEventCount(
  userWallet: string
): Promise<{ created: number; remaining: number }> {
  try {
    // 获取用户的事件限制数量（从服务器）
    const maxEventsPerUser = await fetchUserEventLimit(userWallet);

    const result = await request({
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetEventsList`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        address: userWallet,
        limit: 1000, // 获取所有事件来计数
        offset: 0,
        where: {},
      },
    });

    const userEvents = result.data.events.filter((event: any) => event.updated_by === userWallet);
    const created = userEvents.length;
    const remaining = Math.max(0, maxEventsPerUser - created);

    return { created, remaining };
  } catch (error) {
    console.error('Failed to fetch user event count:', error);
    const defaultLimit = parseInt(import.meta.env.VITE_MAX_EVENTS_PER_USER || '3', 10);
    return { created: 0, remaining: defaultLimit }; // 默认值
  }
}

export async function releaseEvent(authHeaderBase64: string) {
  return request(
    {
      url: `${import.meta.env.VITE_PDONE_ENDPOINT}/create-event`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `${authHeaderBase64}`,
      },
    },
    'Event published successfully'
  );
}

export const customUpload = async ({ file, onSuccess, onError, prefix }: any) => {
  if (!file || !prefix) {
    console.error('File or prefix is missing');
    onError(new Error('File or prefix is missing'));
    return;
  }

  try {
    const fileBuffer = await file.arrayBuffer();
    const hexData = Array.from(new Uint8Array(fileBuffer))
      .map((byte) => byte.toString(16).padStart(2, '0'))
      .join('');

    const response = await request({
      url: `${import.meta.env.VITE_PDONE_ENDPOINT}/upload_image`,
      method: 'post',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json, text/plain, */*',
      },
      data: {
        prefix,
        data: hexData,
      },
    });

    onSuccess(response.data);
  } catch (error) {
    onError(error);
  }
};
