type EventParams = {
  ticker: string;
  slug: string;
  title: string;
  description: string;
  resolution_source: string;
  start_date: string;
  end_date: string;
  image: string;
  icon: string;
  active: boolean;
  closed: boolean;
  archived: boolean;
  new: boolean;
  featured: boolean;
  restricted: boolean;
  liquidity: number;
  volume: number;
  open_interest: number;
  sort_by: string;
  comments_enabled: boolean;
  competitive: number;
  volume_24hr: number;
  featured_image: string;
  enable_order_book: boolean;
  liquidity_clob: number;
  sync: boolean;
  neg_risk: boolean;
  neg_risk_market_id: string;
  neg_risk_fee_bips: number;
  comment_count: number;
  cyom: boolean;
  show_all_outcomes: boolean;
  show_market_images: boolean;
  enable_neg_risk: boolean;
  automatically_active: boolean;
  gmp_chart_mode: string;
  rules: string;
};

export type { EventParams };
