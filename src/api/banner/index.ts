import { request, getCookie } from '@/utils';
import { BannerLanguage, BannerRawDataType } from '@/types/banner';

// 获取单个语言的Banner数据
export async function fetchBannerByLanguage(language: BannerLanguage) {
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/getBannerByRegion`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
    params: { region: language },
  });
}

// 获取所有语言的Banner数据，返回分离的格式
export async function fetchBannerAllLanguagesSeparated() {
  const languages: BannerLanguage[] = ['en', 'zh', 'ko'];

  try {
    const promises = languages.map((lang) => fetchBannerByLanguage(lang));
    const results = await Promise.all(promises);

    // 返回分离的语言数据
    const separatedData: { [key in BannerLanguage]: BannerRawDataType[] } = {
      en: [],
      zh: [],
      ko: [],
    };

    results.forEach((result, index) => {
      const language = languages[index];
      const banners: BannerRawDataType[] = result.data?.banner || [];
      separatedData[language] = banners;
    });

    return {
      data: separatedData,
    };
  } catch (error) {
    console.error('Error fetching multi-language banner data:', error);
    throw error;
  }
}

export async function fetchAddBanner(bannerParams: any) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddBanner`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { objects: [bannerParams] },
  }).then((response) => response.data.insert_banner.returning[0]);
}

export async function fetchUpdateBanner(id: number, bannerParams: Partial<BannerRawDataType>) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backUpdateBanner`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: {
      where: {
        id: {
          _eq: id,
        },
      },
      set: { ...bannerParams },
    },
  }).then((response) => response.data.update_banner.returning[0]);
}

export async function fetchDeleteBanner(id: number) {
  const jwtToken = getCookie('jwt-token');
  return request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backDeleteBanner`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: {
      where: {
        id: {
          _eq: id,
        },
      },
    },
  });
}
