import { request, getCookie } from '@/utils';

export async function fetchGetQuestionByTokenId(tokenId: String) {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetQuestionByTokenId?tokenId=${tokenId}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function fetchGetQuestionByQuestionId(questionId: String) {
  return await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetQuestionByQuestionId?questionId=${questionId}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });
}

export async function fetchGetWebUsers(proxy_wallet: String) {
  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetWebUser?proxy_wallet=${proxy_wallet}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });

  return response.data.web_users[0];
}

export async function fetchUpdateInvitationCode(
  proxy_wallet: String,
  invitation_code: String,
  setInvitationCode: any
) {
  const jwtToken = getCookie('jwt-token');

  await request(
    {
      url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backUpdateInvitationCode`,
      method: 'post',
      headers: { 'Content-Type': 'application/json', Authorization: `Bearer ${jwtToken}` },
      data: { proxy_wallet: proxy_wallet, invitation_code: invitation_code },
    },
    'Invitation code created successfully!'
  );

  setInvitationCode(invitation_code);
}

export async function fetchCheckInviteDuplicate(invitation_code: String) {
  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backCheckInviteDuplicate?invitation_code=${invitation_code}`,
    method: 'get',
    headers: { 'Content-Type': 'application/json' },
  });

  return response.data.length > 0;
}

export async function fetchAddPromotion(objects: Array<{ proxy_wallet: string; amount: number }>) {
  const jwtToken = getCookie('jwt-token');

  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backAddPromotion`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${jwtToken}`,
    },
    data: { objects },
  });

  return response.data.affected_rows;
}

export async function fetchAllPromotions() {
  const response = await request({
    url: `${import.meta.env.VITE_HASURA_ENDPOINT}/backGetPromitionList`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  return response.data.promotion;
}
