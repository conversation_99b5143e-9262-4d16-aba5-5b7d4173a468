.hidden {
  display: none;
}

.markdown-content {
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif; /* 使用系统字体栈 */
}

.markdown-content h1 {
  font-size: 3em;
  margin-bottom: 8px;
}

.markdown-content h2 {
  font-size: 2.5em;
  margin-bottom: 4px;
}

.markdown-content h3 {
  font-size: 2em;
  margin-bottom: 2px;
}

.markdown-content img {
  max-width: 1000px;
  width: 100%;
  height: auto;
  border-radius: 8px; /* 圆角 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影 */
  display: block;
  margin: 2px auto;
}

.markdown-content strong {
  color: red;
}
