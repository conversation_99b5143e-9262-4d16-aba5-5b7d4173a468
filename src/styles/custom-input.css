/* 自定义Input样式 - 全局复用，使用更具体的选择器避免影响其他组件 */
.ant-form-item.custom-input .ant-input {
  border-radius: 6px !important;
  font-size: 18px !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  transition: all 0.2s ease !important;
  color: #1f2937 !important;
  height: 64px !important;
  box-sizing: border-box !important;
}

/* 支持带前缀/后缀的Input */
.ant-form-item.custom-input .ant-input-affix-wrapper {
  border: none !important;
  outline: none !important;
  box-shadow:
    0 0 0 2px #e5e7eb,
    0 2px 8px rgba(0, 0, 0, 0.04) !important;
  border-radius: 8px !important;
  font-size: 18px !important;
  line-height: 1 !important;
  transition: all 0.2s ease !important;
  color: #1f2937 !important;
  height: 64px !important;
  box-sizing: border-box !important;
}

/* 确保内部input元素样式一致 */
.ant-form-item.custom-input .ant-input-affix-wrapper .ant-input {
  font-size: 18px !important;
  font-weight: 500 !important;
  line-height: 1 !important;
  color: #1f2937 !important;
  height: 100% !important;
}
