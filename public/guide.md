**How to Create Your Own Market on Predict.one**

Below is an example of how your market will look once it’s successfully added:
[Example Market Link](https://test.predict.one/event/Tiktok_1737190570247?tid=39&conditionid=6a396d05c5914465f4666d9f770f8b8d8d89e5ffa5ad6aa9bbeb2c1b3d8d92b8&lng=en)

**Will TikTok be banned in the US before May 2025?**

Now, let's walk through the steps to add your own market:

# Step-by-Step Guide

## 1. Access the Admin Portal

The first step is to log in to the Predict.one Admin Portal. Here’s the link:
[Admin Login Link](https://admin-test.predict.one/#/login)

![01](https://sgp1.vultrobjects.com/backend/01-login.png)

## 2. Create a Tag for Your Market

Every market should be assigned a unique tag that reflects the topic. This tag will help categorize your market. For example, we’ve used "TikTok" as the tag in our demo.
![02](https://sgp1.vultrobjects.com/backend/02-add-tag.png)

## 3. Add Your Event (Market)

After creating your tag, the next step is to create the event—this is essentially the market you're setting up. You can add relevant details about your market during this step.
![03](https://sgp1.vultrobjects.com/backend/03-add-event.png)

### 3.1. Publish Your Event

Once the event (market) is set up, you’ll need to publish it. Publishing the event requires your digital signature to confirm and finalize the details on the blockchain.
![04](https://sgp1.vultrobjects.com/backend/04-publish-event.png)
![05](https://sgp1.vultrobjects.com/backend/05-signature.png)

### 3.2. Configure Market Questions

Now that your market is live, it’s time to configure the questions. In this system, the event represents your market, and the questions are the individual items or prediction topics within that market, which users can trade on.
![06](https://sgp1.vultrobjects.com/backend/06-pdone-question.png)
![07](https://sgp1.vultrobjects.com/backend/07-question.png)
![08](https://sgp1.vultrobjects.com/backend/08-add-question.png)

### 3.3. Publish Questions

Each question you add also needs your signature to be recorded on the blockchain. Once published, these questions are available for traders to start making predictions on.
![09](https://sgp1.vultrobjects.com/backend/09-publish-question.png)

## 4. View Your Market on Predict.one

After publishing both your event and questions, your market will be available on the Predict.one platform. You can now see it listed among other active markets. The link is here Example Market
![11](https://sgp1.vultrobjects.com/backend/11-your-event.png)

## 5. Promote Your Market

With your market live, you can share the link to encourage others to engage and trade predictions on your market. This is where you can invite users to participate and boost activity in your market.
![12](https://sgp1.vultrobjects.com/backend/12-share-url.png)
![13](https://sgp1.vultrobjects.com/backend/13-post-to-X.png)

## 6. Publish Result & Earn your fees

Only publish results once the event is complete. False results can be challenged by anyone, and you risk losing your staking, credibility and reputation if the results are found to be malicious.
![10](https://sgp1.vultrobjects.com/backend/10-publish-result.png)

# Sport

## 一、Banner

位置：https://alpha.predict.one/console/data/default/schema/public/tables/banner/browse

![11](https://i.postimg.cc/9MD42JRB/sport-1.webp)

![12](https://i.postimg.cc/FzmQpK4J/sport-2.webp)

1. 其中 event_id 比较重要，指你希望跳转到哪个事件的详情页
2. 如果想看到最佳效果，最好配置 4 个及以上

## 二、Sports

配置 Tag（只需做一遍，后续复用）
xxx_games 是 sport_games 的子集

**选 xxx_games 的 event 一定要选 sport_games**

一级：sport_games
二级：nba_games、epl_games
如下图，需要把 tag_id如47、46、45 给 @Rick

![12](https://i.postimg.cc/L5TFbM9Q/sport-3.webp)

### 01｜足球（多Question）

https://admin.predict.one/#/events
第一步：配置 Event 并 Publish
![13](https://i.postimg.cc/wB0Jg1W9/2025-03-10-17-19-40.png)

第二步：配置 Question 并 Publish

![14](https://i.postimg.cc/dDPZ4r0j/2025-03-10-17-20-42.png)

注意：每个队一个 Question、平局也配置一个 Question

![15](https://i.postimg.cc/SN6fHxpx/sport-4.webp)

### 02｜篮球（单Question）

第一步：配置 Event 并 Publish

![16](https://i.postimg.cc/fbL737SV/2025-03-10-17-22-07.png)

第二步：配置 Question 并 Publish

![17](https://i.postimg.cc/1zjGDgDJ/2025-03-10-17-22-20.png)

跟 NBA 的 event 基本一样
