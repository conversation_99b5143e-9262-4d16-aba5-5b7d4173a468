# Pdone Admin

## ⚡ 简介

用于管理 Prediction One 网站的后台。

## 🚀 开发指南

- **Node.js**：版本需为 `18.x` 或 `20+`

## 安装依赖

### 前端项目依赖

```bash
yarn install
```

### Node 服务依赖

```bash
cd server/
yarn install
```

## 启动服务

### 连接开发环境 (dev hasura 表)

```bash
VITE_ENV=dev npm run dev
```

### 连接生产环境 (ol hasura 表)

```bash
VITE_ENV=ol npm run dev
```

本地开发时，同时需启动本地服务端功能

```bash
cd server/
npm run dev
```

## 📝 Git 提交规范

- **`feat`**：增加新的业务功能
- **`fix`**：修复业务问题或 BUG
- **`perf`**：优化性能
- **`style`**：更改代码风格，不影响运行结果
- **`refactor`**：重构代码
- **`revert`**：撤销更改
- **`test`**：测试相关，不涉及业务代码的更改
- **`docs`**：文档和注释相关
- **`chore`**：更新依赖或修改脚手架配置等琐事
- **`workflow`**：工作流改进
- **`ci`**：持续集成相关
- **`types`**：类型定义文件更改
- **`wip`**：开发中
