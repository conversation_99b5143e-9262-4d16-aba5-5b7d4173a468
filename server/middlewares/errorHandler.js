export function handleError(err, req, res, next) {
  console.error('Error:', err);
  if (err.response) {
    console.error('Response data:', err.response.data);
    console.error('Response status:', err.response.status);
    console.error('Response headers:', err.response.headers);
    res.status(err.response.status).json({ error: err.response.data });
  } else {
    res.status(500).json({ error: 'Internal Server Error' });
  }
}
