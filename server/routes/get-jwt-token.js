import axios from 'axios';
import dotenv from 'dotenv';
import { Router } from 'express';
import { createL2Headers } from 'pedone-clob-client';

dotenv.config();

const router = Router();

router.post('/get-jwt-token', async (req, res, next) => {
  const { signer, creds, role, timestamp } = req.body;

  try {
    // 为 signer 添加 getAddress 方法
    if (!signer.getAddress) {
      signer.getAddress = async () => signer.address;
    }

    const l2HeaderArgs = {
      method: 'GET',
      requestPath: '/auth/hasura-jwt',
    };

    // 调用 createL2Headers
    const l2Headers = await createL2Headers(
      signer,
      {
        key: creds.apiKey,
        secret: creds.secret,
        passphrase: creds.passphrase,
      },
      l2HeaderArgs,
      timestamp
    );

    const tokenResponse = await axios({
      url: `${process.env.VITE_API_PREFIX}/auth/hasura-jwt`,
      method: 'get',
      headers: {
        'Content-Type': 'application/json',
        ...l2Headers,
      },
      params: {
        role: role,
        expire_after_min: 60 * 24 * 3,
      },
    });

    res.json({
      jwt: tokenResponse.data.jwt,
    });
  } catch (error) {
    console.error('Error in getJWTToken:', error);
    next(error);
  }
});

export default router;
