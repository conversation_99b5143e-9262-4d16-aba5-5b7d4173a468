import dotenv from 'dotenv';
import express from 'express';
import cors from 'cors';
import path from 'path';
import bodyParser from 'body-parser';
import cookieParser from 'cookie-parser';

import getJwtTokenRouter from './routes/get-jwt-token.js';
import { handleError } from './middlewares/errorHandler.js';

// Dynamically determine which .env file to load

// Get the current directory using import.meta.url
const currentDir = path.dirname(new URL(import.meta.url).pathname);

// Determine which .env file to load based on the environment
const envFile = process.env.NODE_ENV === 'production' ? '.env.production' : '.env';

// Load the environment variables from the specific file
dotenv.config({ path: path.resolve(currentDir, envFile) });
const app = express();
const PORT = process.env.PORT || 5001;

const corsOrigins = process.env.CORS_ORIGIN
  ? process.env.CORS_ORIGIN.split(',') // 将逗号分隔的字符串转换为数组
  : process.env.VITE_API_PREFIX;

// 配置 CORS
app.use(
  cors({
    origin: corsOrigins,
    credentials: true, // 允许发送 Cookie
  })
);

app.use(bodyParser.json());
app.use(cookieParser());
// 处理所有的 OPTIONS 请求
app.options('*', cors());
app.use(express.static(path.join(currentDir, 'dist')));

// 使用其他路由
app.use(getJwtTokenRouter);

// 错误处理
app.use(handleError);

app.listen(PORT, '0.0.0.0', () => {
  console.log(`Server is running on port ${PORT}`);
});
