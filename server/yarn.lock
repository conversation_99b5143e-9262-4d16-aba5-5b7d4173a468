# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ethereumjs/rlp@^4.0.1":
  version "4.0.1"

"@ethereumjs/util@^8.0.6":
  version "8.1.0"
  dependencies:
    "@ethereumjs/rlp" "^4.0.1"
    ethereum-cryptography "^2.0.0"
    micro-ftch "^0.3.1"

"@ethersproject/abi@^5.8.0", "@ethersproject/abi@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/hash" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"

"@ethersproject/abstract-provider@^5.8.0", "@ethersproject/abstract-provider@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/networks" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"
    "@ethersproject/web" "^5.8.0"

"@ethersproject/abstract-signer@^5.8.0", "@ethersproject/abstract-signer@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-provider" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"

"@ethersproject/address@^5.8.0", "@ethersproject/address@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/rlp" "^5.8.0"

"@ethersproject/base64@^5.8.0", "@ethersproject/base64@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"

"@ethersproject/basex@^5.8.0", "@ethersproject/basex@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"

"@ethersproject/bignumber@^5.8.0", "@ethersproject/bignumber@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    bn.js "^5.2.1"

"@ethersproject/bytes@^5.8.0", "@ethersproject/bytes@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/constants@^5.8.0", "@ethersproject/constants@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bignumber" "^5.8.0"

"@ethersproject/contracts@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abi" "^5.8.0"
    "@ethersproject/abstract-provider" "^5.8.0"
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"

"@ethersproject/hash@^5.8.0", "@ethersproject/hash@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/base64" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"

"@ethersproject/hdnode@^5.8.0", "@ethersproject/hdnode@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/basex" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/pbkdf2" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/sha2" "^5.8.0"
    "@ethersproject/signing-key" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"
    "@ethersproject/wordlists" "^5.8.0"

"@ethersproject/json-wallets@^5.8.0", "@ethersproject/json-wallets@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/hdnode" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/pbkdf2" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/random" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"
    aes-js "3.0.0"
    scrypt-js "3.0.1"

"@ethersproject/keccak256@^5.8.0", "@ethersproject/keccak256@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    js-sha3 "0.8.0"

"@ethersproject/logger@^5.8.0", "@ethersproject/logger@5.8.0":
  version "5.8.0"

"@ethersproject/networks@^5.8.0", "@ethersproject/networks@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/pbkdf2@^5.8.0", "@ethersproject/pbkdf2@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/sha2" "^5.8.0"

"@ethersproject/properties@^5.8.0", "@ethersproject/properties@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/providers@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-provider" "^5.8.0"
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/base64" "^5.8.0"
    "@ethersproject/basex" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/hash" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/networks" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/random" "^5.8.0"
    "@ethersproject/rlp" "^5.8.0"
    "@ethersproject/sha2" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"
    "@ethersproject/web" "^5.8.0"
    bech32 "1.1.4"
    ws "8.18.0"

"@ethersproject/random@^5.8.0", "@ethersproject/random@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/rlp@^5.8.0", "@ethersproject/rlp@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/sha2@^5.8.0", "@ethersproject/sha2@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    hash.js "1.1.7"

"@ethersproject/signing-key@^5.8.0", "@ethersproject/signing-key@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    bn.js "^5.2.1"
    elliptic "6.6.1"
    hash.js "1.1.7"

"@ethersproject/solidity@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/sha2" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"

"@ethersproject/strings@^5.8.0", "@ethersproject/strings@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/transactions@^5.8.0", "@ethersproject/transactions@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/rlp" "^5.8.0"
    "@ethersproject/signing-key" "^5.8.0"

"@ethersproject/units@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/constants" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"

"@ethersproject/wallet@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/abstract-provider" "^5.8.0"
    "@ethersproject/abstract-signer" "^5.8.0"
    "@ethersproject/address" "^5.8.0"
    "@ethersproject/bignumber" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/hash" "^5.8.0"
    "@ethersproject/hdnode" "^5.8.0"
    "@ethersproject/json-wallets" "^5.8.0"
    "@ethersproject/keccak256" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/random" "^5.8.0"
    "@ethersproject/signing-key" "^5.8.0"
    "@ethersproject/transactions" "^5.8.0"
    "@ethersproject/wordlists" "^5.8.0"

"@ethersproject/web@^5.8.0", "@ethersproject/web@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/base64" "^5.8.0"
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"

"@ethersproject/wordlists@^5.8.0", "@ethersproject/wordlists@5.8.0":
  version "5.8.0"
  dependencies:
    "@ethersproject/bytes" "^5.8.0"
    "@ethersproject/hash" "^5.8.0"
    "@ethersproject/logger" "^5.8.0"
    "@ethersproject/properties" "^5.8.0"
    "@ethersproject/strings" "^5.8.0"

"@metamask/eth-sig-util@^5.0.0":
  version "5.1.0"
  dependencies:
    "@ethereumjs/util" "^8.0.6"
    bn.js "^4.12.0"
    ethereum-cryptography "^2.0.0"
    ethjs-util "^0.1.6"
    tweetnacl "^1.0.3"
    tweetnacl-util "^0.15.1"

"@noble/curves@~1.4.0", "@noble/curves@1.4.2":
  version "1.4.2"
  dependencies:
    "@noble/hashes" "1.4.0"

"@noble/hashes@~1.4.0", "@noble/hashes@1.4.0":
  version "1.4.0"

"@polymarket/order-utils@^2.1.0":
  version "2.1.0"
  dependencies:
    "@metamask/eth-sig-util" "^5.0.0"
    ethers "^5.7.1"

"@scure/base@~1.1.6":
  version "1.1.9"

"@scure/bip32@1.4.0":
  version "1.4.0"
  dependencies:
    "@noble/curves" "~1.4.0"
    "@noble/hashes" "~1.4.0"
    "@scure/base" "~1.1.6"

"@scure/bip39@1.3.0":
  version "1.3.0"
  dependencies:
    "@noble/hashes" "~1.4.0"
    "@scure/base" "~1.1.6"

accepts@~1.3.8:
  version "1.3.8"
  dependencies:
    mime-types "~2.1.34"
    negotiator "0.6.3"

aes-js@3.0.0:
  version "3.0.0"

anymatch@~3.1.2:
  version "3.1.3"
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

append-field@^1.0.0:
  version "1.0.0"

array-flatten@1.1.1:
  version "1.1.1"

asn1.js@^5.4.1:
  version "5.4.1"
  dependencies:
    bn.js "^4.0.0"
    inherits "^2.0.1"
    minimalistic-assert "^1.0.0"
    safer-buffer "^2.1.0"

asynckit@^0.4.0:
  version "0.4.0"

axios@^0.27.2:
  version "0.27.2"
  dependencies:
    follow-redirects "^1.14.9"
    form-data "^4.0.0"

axios@^1.6.8:
  version "1.7.9"
  dependencies:
    follow-redirects "^1.15.6"
    form-data "^4.0.0"
    proxy-from-env "^1.1.0"

bagpipe@^0.3.5:
  version "0.3.5"

balanced-match@^1.0.0:
  version "1.0.2"

bcryptjs@^2.4.3:
  version "2.4.3"

bech32@1.1.4:
  version "1.1.4"

binary-extensions@^2.0.0:
  version "2.3.0"

bn.js@^4.0.0:
  version "4.12.1"

bn.js@^4.11.9, bn.js@^4.12.0:
  version "4.12.2"

bn.js@^5.2.1:
  version "5.2.2"

body-parser@^1.20.3, body-parser@1.20.3:
  version "1.20.3"
  dependencies:
    bytes "3.1.2"
    content-type "~1.0.5"
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    on-finished "2.4.1"
    qs "6.13.0"
    raw-body "2.5.2"
    type-is "~1.6.18"
    unpipe "1.0.0"

brace-expansion@^1.1.7:
  version "1.1.11"
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

braces@~3.0.2:
  version "3.0.3"
  dependencies:
    fill-range "^7.1.1"

brorand@^1.1.0:
  version "1.1.0"

browser-or-node@^2.1.1:
  version "2.1.1"

buffer-from@^1.0.0:
  version "1.1.2"

busboy@^1.0.0:
  version "1.6.0"
  dependencies:
    streamsearch "^1.1.0"

bytes@3.1.2:
  version "3.1.2"

call-bind-apply-helpers@^1.0.1:
  version "1.0.1"
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bound@^1.0.2:
  version "1.0.3"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    get-intrinsic "^1.2.6"

chokidar@^3.5.2:
  version "3.6.0"
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

combined-stream@^1.0.8:
  version "1.0.8"
  dependencies:
    delayed-stream "~1.0.0"

concat-map@0.0.1:
  version "0.0.1"

concat-stream@^1.5.2:
  version "1.6.2"
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

content-disposition@0.5.4:
  version "0.5.4"
  dependencies:
    safe-buffer "5.2.1"

content-type@~1.0.4, content-type@~1.0.5:
  version "1.0.5"

cookie-parser@^1.4.7:
  version "1.4.7"
  dependencies:
    cookie "0.7.2"
    cookie-signature "1.0.6"

cookie-signature@1.0.6:
  version "1.0.6"

cookie-signature@1.0.7:
  version "1.0.7"

cookie@0.7.1:
  version "0.7.1"

cookie@0.7.2:
  version "0.7.2"

core-util-is@~1.0.0:
  version "1.0.3"

cors@^2.8.5:
  version "2.8.5"
  dependencies:
    object-assign "^4"
    vary "^1"

debug@^4:
  version "4.4.0"
  dependencies:
    ms "^2.1.3"

debug@2.6.9:
  version "2.6.9"
  dependencies:
    ms "2.0.0"

delayed-stream@~1.0.0:
  version "1.0.0"

depd@~2.0.0, depd@2.0.0:
  version "2.0.0"

destroy@1.2.0:
  version "1.2.0"

dotenv@^16.4.7:
  version "16.4.7"

dunder-proto@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

ee-first@1.1.1:
  version "1.1.1"

elliptic@6.6.1:
  version "6.6.1"
  dependencies:
    bn.js "^4.11.9"
    brorand "^1.1.0"
    hash.js "^1.0.0"
    hmac-drbg "^1.0.1"
    inherits "^2.0.4"
    minimalistic-assert "^1.0.1"
    minimalistic-crypto-utils "^1.0.1"

encodeurl@~1.0.2:
  version "1.0.2"

encodeurl@~2.0.0:
  version "2.0.0"

es-define-property@^1.0.1:
  version "1.0.1"

es-errors@^1.3.0:
  version "1.3.0"

es-object-atoms@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"

escape-html@~1.0.3:
  version "1.0.3"

etag@~1.8.1:
  version "1.8.1"

ethereum-cryptography@^2.0.0:
  version "2.2.1"
  dependencies:
    "@noble/curves" "1.4.2"
    "@noble/hashes" "1.4.0"
    "@scure/bip32" "1.4.0"
    "@scure/bip39" "1.3.0"

ethers@^5.7.1:
  version "5.8.0"
  dependencies:
    "@ethersproject/abi" "5.8.0"
    "@ethersproject/abstract-provider" "5.8.0"
    "@ethersproject/abstract-signer" "5.8.0"
    "@ethersproject/address" "5.8.0"
    "@ethersproject/base64" "5.8.0"
    "@ethersproject/basex" "5.8.0"
    "@ethersproject/bignumber" "5.8.0"
    "@ethersproject/bytes" "5.8.0"
    "@ethersproject/constants" "5.8.0"
    "@ethersproject/contracts" "5.8.0"
    "@ethersproject/hash" "5.8.0"
    "@ethersproject/hdnode" "5.8.0"
    "@ethersproject/json-wallets" "5.8.0"
    "@ethersproject/keccak256" "5.8.0"
    "@ethersproject/logger" "5.8.0"
    "@ethersproject/networks" "5.8.0"
    "@ethersproject/pbkdf2" "5.8.0"
    "@ethersproject/properties" "5.8.0"
    "@ethersproject/providers" "5.8.0"
    "@ethersproject/random" "5.8.0"
    "@ethersproject/rlp" "5.8.0"
    "@ethersproject/sha2" "5.8.0"
    "@ethersproject/signing-key" "5.8.0"
    "@ethersproject/solidity" "5.8.0"
    "@ethersproject/strings" "5.8.0"
    "@ethersproject/transactions" "5.8.0"
    "@ethersproject/units" "5.8.0"
    "@ethersproject/wallet" "5.8.0"
    "@ethersproject/web" "5.8.0"
    "@ethersproject/wordlists" "5.8.0"

ethjs-util@^0.1.6:
  version "0.1.6"
  dependencies:
    is-hex-prefixed "1.0.0"
    strip-hex-prefix "1.0.0"

express-session@^1.18.1:
  version "1.18.1"
  dependencies:
    cookie "0.7.2"
    cookie-signature "1.0.7"
    debug "2.6.9"
    depd "~2.0.0"
    on-headers "~1.0.2"
    parseurl "~1.3.3"
    safe-buffer "5.2.1"
    uid-safe "~2.1.5"

express@^4.21.2:
  version "4.21.2"
  dependencies:
    accepts "~1.3.8"
    array-flatten "1.1.1"
    body-parser "1.20.3"
    content-disposition "0.5.4"
    content-type "~1.0.4"
    cookie "0.7.1"
    cookie-signature "1.0.6"
    debug "2.6.9"
    depd "2.0.0"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    etag "~1.8.1"
    finalhandler "1.3.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    merge-descriptors "1.0.3"
    methods "~1.1.2"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    path-to-regexp "0.1.12"
    proxy-addr "~2.0.7"
    qs "6.13.0"
    range-parser "~1.2.1"
    safe-buffer "5.2.1"
    send "0.19.0"
    serve-static "1.16.2"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    type-is "~1.6.18"
    utils-merge "1.0.1"
    vary "~1.1.2"

fill-range@^7.1.1:
  version "7.1.1"
  dependencies:
    to-regex-range "^5.0.1"

finalhandler@1.3.1:
  version "1.3.1"
  dependencies:
    debug "2.6.9"
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    on-finished "2.4.1"
    parseurl "~1.3.3"
    statuses "2.0.1"
    unpipe "~1.0.0"

follow-redirects@^1.14.9, follow-redirects@^1.15.6:
  version "1.15.9"

form-data@^4.0.0:
  version "4.0.1"
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    mime-types "^2.1.12"

forwarded@0.2.0:
  version "0.2.0"

fresh@0.5.2:
  version "0.5.2"

fs-extra@^8.0.1:
  version "8.1.0"
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fsevents@~2.3.2:
  version "2.3.3"

function-bind@^1.1.2:
  version "1.1.2"

get-intrinsic@^1.2.5, get-intrinsic@^1.2.6:
  version "1.2.7"
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    function-bind "^1.1.2"
    get-proto "^1.0.0"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.0:
  version "1.0.1"
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

glob-parent@~5.1.2:
  version "5.1.2"
  dependencies:
    is-glob "^4.0.1"

gopd@^1.2.0:
  version "1.2.0"

graceful-fs@^4.1.6, graceful-fs@^4.2.0:
  version "4.2.11"

has-flag@^3.0.0:
  version "3.0.0"

has-symbols@^1.1.0:
  version "1.1.0"

hash.js@^1.0.0, hash.js@^1.0.3, hash.js@1.1.7:
  version "1.1.7"
  dependencies:
    inherits "^2.0.3"
    minimalistic-assert "^1.0.1"

hasown@^2.0.2:
  version "2.0.2"
  dependencies:
    function-bind "^1.1.2"

hmac-drbg@^1.0.1:
  version "1.0.1"
  dependencies:
    hash.js "^1.0.3"
    minimalistic-assert "^1.0.0"
    minimalistic-crypto-utils "^1.0.1"

http-errors@2.0.0:
  version "2.0.0"
  dependencies:
    depd "2.0.0"
    inherits "2.0.4"
    setprototypeof "1.2.0"
    statuses "2.0.1"
    toidentifier "1.0.1"

iconv-lite@0.4.24:
  version "0.4.24"
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ignore-by-default@^1.0.1:
  version "1.0.1"

imurmurhash@^0.1.4:
  version "0.1.4"

inherits@^2.0.1, inherits@^2.0.3, inherits@^2.0.4, inherits@~2.0.3, inherits@2.0.4:
  version "2.0.4"

ipaddr.js@1.9.1:
  version "1.9.1"

is-binary-path@~2.1.0:
  version "2.1.0"
  dependencies:
    binary-extensions "^2.0.0"

is-extglob@^2.1.1:
  version "2.1.1"

is-glob@^4.0.1, is-glob@~4.0.1:
  version "4.0.3"
  dependencies:
    is-extglob "^2.1.1"

is-hex-prefixed@1.0.0:
  version "1.0.0"

is-number@^7.0.0:
  version "7.0.0"

is-typedarray@^1.0.0:
  version "1.0.0"

isarray@~1.0.0:
  version "1.0.0"

js-sha3@0.8.0:
  version "0.8.0"

jsonfile@^4.0.0:
  version "4.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

kruptein@^2.0.4:
  version "2.2.3"
  dependencies:
    asn1.js "^5.4.1"

math-intrinsics@^1.1.0:
  version "1.1.0"

media-typer@0.3.0:
  version "0.3.0"

merge-descriptors@1.0.3:
  version "1.0.3"

methods@~1.1.2:
  version "1.1.2"

micro-ftch@^0.3.1:
  version "0.3.1"

mime-db@1.52.0:
  version "1.52.0"

mime-types@^2.1.12, mime-types@~2.1.24, mime-types@~2.1.34:
  version "2.1.35"
  dependencies:
    mime-db "1.52.0"

mime@1.6.0:
  version "1.6.0"

minimalistic-assert@^1.0.0, minimalistic-assert@^1.0.1:
  version "1.0.1"

minimalistic-crypto-utils@^1.0.1:
  version "1.0.1"

minimatch@^3.1.2:
  version "3.1.2"
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.6:
  version "1.2.8"

mkdirp@^0.5.4:
  version "0.5.6"
  dependencies:
    minimist "^1.2.6"

ms@^2.1.3, ms@2.1.3:
  version "2.1.3"

ms@2.0.0:
  version "2.0.0"

multer@^1.4.5-lts.1:
  version "1.4.5-lts.1"
  dependencies:
    append-field "^1.0.0"
    busboy "^1.0.0"
    concat-stream "^1.5.2"
    mkdirp "^0.5.4"
    object-assign "^4.1.1"
    type-is "^1.6.4"
    xtend "^4.0.0"

negotiator@0.6.3:
  version "0.6.3"

nodemon@^3.1.7:
  version "3.1.9"
  dependencies:
    chokidar "^3.5.2"
    debug "^4"
    ignore-by-default "^1.0.1"
    minimatch "^3.1.2"
    pstree.remy "^1.1.8"
    semver "^7.5.3"
    simple-update-notifier "^2.0.0"
    supports-color "^5.5.0"
    touch "^3.1.0"
    undefsafe "^2.0.5"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"

object-assign@^4, object-assign@^4.1.1:
  version "4.1.1"

object-inspect@^1.13.3:
  version "1.13.3"

on-finished@2.4.1:
  version "2.4.1"
  dependencies:
    ee-first "1.1.1"

on-headers@~1.0.2:
  version "1.0.2"

parseurl@~1.3.3:
  version "1.3.3"

path-to-regexp@0.1.12:
  version "0.1.12"

pedone-clob-client@^1.0.1:
  version "1.0.1"
  dependencies:
    "@polymarket/order-utils" "^2.1.0"
    axios "^0.27.2"
    browser-or-node "^2.1.1"
    ethers "^5.7.1"

picomatch@^2.0.4, picomatch@^2.2.1:
  version "2.3.1"

process-nextick-args@~2.0.0:
  version "2.0.1"

proxy-addr@~2.0.7:
  version "2.0.7"
  dependencies:
    forwarded "0.2.0"
    ipaddr.js "1.9.1"

proxy-from-env@^1.1.0:
  version "1.1.0"

pstree.remy@^1.1.8:
  version "1.1.8"

qs@6.13.0:
  version "6.13.0"
  dependencies:
    side-channel "^1.0.6"

random-bytes@~1.0.0:
  version "1.0.0"

range-parser@~1.2.1:
  version "1.2.1"

raw-body@2.5.2:
  version "2.5.2"
  dependencies:
    bytes "3.1.2"
    http-errors "2.0.0"
    iconv-lite "0.4.24"
    unpipe "1.0.0"

readable-stream@^2.2.2:
  version "2.3.8"
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readdirp@~3.6.0:
  version "3.6.0"
  dependencies:
    picomatch "^2.2.1"

retry@^0.12.0:
  version "0.12.0"

safe-buffer@~5.1.0:
  version "5.1.2"

safe-buffer@~5.1.1:
  version "5.1.2"

safe-buffer@5.2.1:
  version "5.2.1"

safer-buffer@^2.1.0, "safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"

scrypt-js@3.0.1:
  version "3.0.1"

semver@^7.5.3:
  version "7.6.3"

send@0.19.0:
  version "0.19.0"
  dependencies:
    debug "2.6.9"
    depd "2.0.0"
    destroy "1.2.0"
    encodeurl "~1.0.2"
    escape-html "~1.0.3"
    etag "~1.8.1"
    fresh "0.5.2"
    http-errors "2.0.0"
    mime "1.6.0"
    ms "2.1.3"
    on-finished "2.4.1"
    range-parser "~1.2.1"
    statuses "2.0.1"

serve-static@1.16.2:
  version "1.16.2"
  dependencies:
    encodeurl "~2.0.0"
    escape-html "~1.0.3"
    parseurl "~1.3.3"
    send "0.19.0"

session-file-store@^1.0.1:
  version "1.5.0"
  dependencies:
    bagpipe "^0.3.5"
    fs-extra "^8.0.1"
    kruptein "^2.0.4"
    object-assign "^4.1.1"
    retry "^0.12.0"
    write-file-atomic "3.0.3"

setprototypeof@1.2.0:
  version "1.2.0"

side-channel-list@^1.0.0:
  version "1.0.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.0.6:
  version "1.1.0"
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

signal-exit@^3.0.2:
  version "3.0.7"

simple-update-notifier@^2.0.0:
  version "2.0.0"
  dependencies:
    semver "^7.5.3"

statuses@2.0.1:
  version "2.0.1"

streamsearch@^1.1.0:
  version "1.1.0"

string_decoder@~1.1.1:
  version "1.1.1"
  dependencies:
    safe-buffer "~5.1.0"

strip-hex-prefix@1.0.0:
  version "1.0.0"
  dependencies:
    is-hex-prefixed "1.0.0"

supports-color@^5.5.0:
  version "5.5.0"
  dependencies:
    has-flag "^3.0.0"

to-regex-range@^5.0.1:
  version "5.0.1"
  dependencies:
    is-number "^7.0.0"

toidentifier@1.0.1:
  version "1.0.1"

touch@^3.1.0:
  version "3.1.1"

tslib@^2.8.1:
  version "2.8.1"
  resolved "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  integrity sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==

tweetnacl-util@^0.15.1:
  version "0.15.1"

tweetnacl@^1.0.3:
  version "1.0.3"

type-is@^1.6.4, type-is@~1.6.18:
  version "1.6.18"
  dependencies:
    media-typer "0.3.0"
    mime-types "~2.1.24"

typedarray-to-buffer@^3.1.5:
  version "3.1.5"
  dependencies:
    is-typedarray "^1.0.0"

typedarray@^0.0.6:
  version "0.0.6"

uid-safe@~2.1.5:
  version "2.1.5"
  dependencies:
    random-bytes "~1.0.0"

undefsafe@^2.0.5:
  version "2.0.5"

universalify@^0.1.0:
  version "0.1.2"

unpipe@~1.0.0, unpipe@1.0.0:
  version "1.0.0"

util-deprecate@~1.0.1:
  version "1.0.2"

utils-merge@1.0.1:
  version "1.0.1"

vary@^1, vary@~1.1.2:
  version "1.1.2"

write-file-atomic@3.0.3:
  version "3.0.3"
  dependencies:
    imurmurhash "^0.1.4"
    is-typedarray "^1.0.0"
    signal-exit "^3.0.2"
    typedarray-to-buffer "^3.1.5"

ws@8.18.0:
  version "8.18.0"

xtend@^4.0.0:
  version "4.0.2"
