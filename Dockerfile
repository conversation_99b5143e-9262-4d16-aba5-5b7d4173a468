FROM node:22.8.0-alpine AS builder

#RUN apk add --no-cache python3 make g++ bash

WORKDIR /app

COPY package.json yarn.lock ./

RUN corepack enable && \
    corepack prepare yarn@3.2.3 --activate

COPY . .
RUN cd server && yarn install

# build the outside app
RUN cd /app && \ 
    yarn install && yarn build

FROM node:22.8.0-alpine
WORKDIR /app
COPY --from=builder /app/server/node_modules ./node_modules
COPY --from=builder /app/server/routes ./routes
COPY --from=builder /app/server/middlewares ./middlewares
COPY --from=builder /app/server/utils ./utils
COPY --from=builder /app/server/yarn.lock ./yarn.lock
COPY --from=builder /app/server/package.json ./package.json
COPY --from=builder /app/server/index.js ./index.js
COPY --from=builder /app/dist ./dist
# Set environment variable for production
ENV NODE_ENV=production

# Expose the port the app will run on
EXPOSE 5001

# Run the application
CMD ["node", "index.js"]
