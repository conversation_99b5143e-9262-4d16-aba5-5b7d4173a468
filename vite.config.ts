import react from '@vitejs/plugin-react';
import { join } from 'node:path';
import { cwd } from 'node:process';
import { defineConfig, loadEnv, type CommonServerOptions } from 'vite';

export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, cwd(), '');

  // 从 process.env 中读取 VITE_ENV
  const viteEnv = process.env.VITE_ENV || env.VITE_ENV;

  // 根据 VITE_ENV 动态选择配置
  const isDev = viteEnv === 'dev';
  const SERVER_URL = isDev ? env.DEV_SERVER_URL : env.OL_SERVER_URL || 'https://admin.predict.one';
  const HASURA_ENDPOINT = isDev
    ? env.DEV_HASURA_ENDPOINT
    : env.OL_HASURA_ENDPOINT || 'https://alpha.predict.one/api/rest';
  const BACK_SIGNATURE_DOMAIN = isDev
    ? env.DEV_BACK_SIGNATURE_DOMAIN
    : env.OL_BACK_SIGNATURE_DOMAIN || 'admin.predict.one';
  const BACK_SIGNATURE_URL = isDev
    ? env.DEV_BACK_SIGNATURE_URL
    : env.OL_BACK_SIGNATURE_URL || 'https://admin.predict.one';
  const PDONE_ENDPOINT = isDev
    ? env.DEV_PDONE_ENDPOINT
    : env.OL_PDONE_ENDPOINT || 'https://api.predict.one';
  const CHAIN_ID = isDev ? env.DEV_CHAIN_ID : env.OL_CHAIN_ID || 8453;

  const SERVER_OPTIONS: CommonServerOptions = {
    port: parseInt(env.PORT, 10), // 从环境变量中读取端口号并转换为整数
    strictPort: true, // 如果端口被占用，则直接报错而不是尝试使用其他端口
  };

  return {
    base: env.VITE_WEB_BASE,
    server: SERVER_OPTIONS,
    preview: SERVER_OPTIONS,
    resolve: {
      alias: {
        '@': join(cwd(), 'src'),
      },
    },
    build: {
      chunkSizeWarningLimit: Infinity,
      reportCompressedSize: false,
      rollupOptions: {
        onwarn: (warning, defaultHandler) => {
          if (warning.code === 'INVALID_ANNOTATION' && warning.message.includes('/*#__PURE__*/')) {
            return;
          }
          defaultHandler(warning);
        },
      },
    },
    plugins: [react()],
    define: {
      // 动态注入 import.meta.env 的值
      'import.meta.env.VITE_SERVER_URL': JSON.stringify(SERVER_URL),
      'import.meta.env.VITE_HASURA_ENDPOINT': JSON.stringify(HASURA_ENDPOINT),
      'import.meta.env.VITE_BACK_SIGNATURE_DOMAIN': JSON.stringify(BACK_SIGNATURE_DOMAIN),
      'import.meta.env.VITE_BACK_SIGNATURE_URL': JSON.stringify(BACK_SIGNATURE_URL),
      'import.meta.env.VITE_PDONE_ENDPOINT': JSON.stringify(PDONE_ENDPOINT),
      'import.meta.env.VITE_CHAIN_ID': JSON.stringify(CHAIN_ID),
    },
  };
});
